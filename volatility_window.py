def select_today_window(binance_trader, symbol):
    """
    根据历史波动率选择当天的最佳交易窗口
    选择过去48小时内波动率最高的连续7小时作为交易窗口
    """
    # 获取过去48小时的1小时K线数据
    k1h = binance_trader.get_klines(symbol, '1h', 48)   # 前 48 h
    vol = [float(k[5]) for k in k1h]  # 第6列是成交量
    
    # 计算滚动7小时的波动率（用成交量近似）
    rolling_7h_vol = [sum(vol[i:i+7]) for i in range(len(vol)-7)]
    
    # 找到波动率最高的7小时窗口
    max_idx = rolling_7h_vol.index(max(rolling_7h_vol))
    start_hour = (max_idx) % 24
    end_hour = (max_idx + 7) % 24
    
    return {"start_hour": start_hour, "end_hour": end_hour, "start_minute": 0, "end_minute": 0}


def select_night_session():
    """
    返回夜盘交易时段配置（北京时间21:30-04:00）
    """
    return {
        "start_hour": 13,      # UTC 13:30 = 北京时间 21:30
        "start_minute": 30,
        "end_hour": 20,        # UTC 20:00 = 北京时间 04:00
        "end_minute": 0
    }


def select_day_session():
    """
    返回日盘交易时段配置（北京时间09:30-16:00）
    """
    return {
        "start_hour": 1,       # UTC 01:30 = 北京时间 09:30
        "start_minute": 30,
        "end_hour": 8,         # UTC 08:00 = 北京时间 16:00
        "end_minute": 0
    }