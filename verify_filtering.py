#!/usr/bin/env python3
"""
验证策略筛选逻辑的脚本
"""

import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_filtering():
    try:
        # 导入必要的模块
        from day_breakout_strategy import DayBreakoutStrategy
        from binance_trader import BinanceTrader
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        
        # 创建策略实例
        strategy = DayBreakoutStrategy(trader, config)
        
        # 获取所有交易对
        all_futures = trader.get_all_futures()
        print(f"总共获取到 {len(all_futures)} 个交易对")
        
        # 手动过滤新币和已开放杠杆的交易对
        filtered_futures = []
        for symbol in all_futures[:50]:  # 只检查前50个以节省时间
            is_new = strategy._is_new_coin(symbol)
            has_leverage = strategy._has_open_leverage(symbol)
            
            if is_new and has_leverage:
                filtered_futures.append(symbol)
                
        print(f"过滤后符合条件的交易对: {len(filtered_futures)}")
        print(f"符合条件的交易对列表: {filtered_futures}")
        
        # 分析为什么过滤后数量很少
        if filtered_futures:
            print("\n=== 分析第一个符合条件的交易对 ===")
            symbol = filtered_futures[0]
            
            # 检查新币条件
            klines = trader.get_klines(symbol, interval='1d', limit=100)
            print(f"{symbol} 获取到 {len(klines)} 根日K线")
            print(f"是否为新币 (≤90天): {len(klines) <= 90}")
            
            # 检查5分钟K线
            klines_5m = trader.get_klines(symbol, interval='5m', limit=48)
            print(f"{symbol} 获取到 {len(klines_5m)} 根5分钟K线")
            
            if len(klines_5m) >= 2:
                recent_kline = klines_5m[-1]
                prev_kline = klines_5m[-2]
                
                recent_open = float(recent_kline[1])
                recent_close = float(recent_kline[4])
                recent_change_pct = ((recent_close - recent_open) / recent_open) * 100 if recent_open != 0 else 0
                
                recent_vol = float(recent_kline[5])
                prev_vol = float(prev_kline[5])
                vol_ratio = recent_vol / prev_vol if prev_vol > 0 else 0
                
                print(f"5分钟涨幅: {recent_change_pct:.2f}% (要求≥3%)")
                print(f"成交量比率: {vol_ratio:.2f}x (要求≥1.5x)")
                
                # 检查价格和24小时统计
                ticker = trader.get_symbol_ticker(symbol)
                if ticker and 'price' in ticker:
                    current_price = float(ticker['price'])
                    print(f"当前价格: {current_price:.4f} USDT (要求≤100 USDT)")
                    
                    ticker_24h = trader.get_ticker(symbol)
                    if ticker_24h:
                        price_change_percent = float(ticker_24h.get('priceChangePercent', 0))
                        quote_volume = float(ticker_24h.get('quoteVolume', 0))
                        print(f"24小时涨幅: {price_change_percent:.2f}% (要求≥8%)")
                        print(f"24小时成交量: {quote_volume:.2f} USDT (要求≥1,000,000 USDT)")
        else:
            print("\n没有找到符合条件的交易对，分析原因：")
            
            # 随机选择几个交易对分析原因
            test_symbols = all_futures[:10]
            for symbol in test_symbols:
                print(f"\n--- 分析 {symbol} ---")
                
                # 检查新币条件
                klines = trader.get_klines(symbol, interval='1d', limit=100)
                is_new = len(klines) <= 90 if klines else False
                print(f"是否为新币: {is_new} (K线数量: {len(klines) if klines else 0})")
                
                # 检查杠杆条件
                has_leverage = strategy._has_open_leverage(symbol)
                print(f"是否已开放杠杆: {has_leverage}")
                
                if is_new and has_leverage:
                    print(f"{symbol} 满足基本条件，继续分析详细条件")
                    
                    # 检查5分钟K线条件
                    klines_5m = trader.get_klines(symbol, interval='5m', limit=48)
                    if len(klines_5m) >= 2:
                        recent_kline = klines_5m[-1]
                        prev_kline = klines_5m[-2]
                        
                        recent_open = float(recent_kline[1])
                        recent_close = float(recent_kline[4])
                        recent_change_pct = ((recent_close - recent_open) / recent_open) * 100 if recent_open != 0 else 0
                        
                        recent_vol = float(recent_kline[5])
                        prev_vol = float(prev_kline[5])
                        vol_ratio = recent_vol / prev_vol if prev_vol > 0 else 0
                        
                        print(f"5分钟涨幅: {recent_change_pct:.2f}% (要求≥3%)")
                        print(f"成交量比率: {vol_ratio:.2f}x (要求≥1.5x)")
                        
                        # 检查其他条件
                        ticker = trader.get_symbol_ticker(symbol)
                        if ticker and 'price' in ticker:
                            current_price = float(ticker['price'])
                            print(f"当前价格: {current_price:.4f} USDT (要求≤100 USDT)")
                            
                            ticker_24h = trader.get_ticker(symbol)
                            if ticker_24h:
                                price_change_percent = float(ticker_24h.get('priceChangePercent', 0))
                                quote_volume = float(ticker_24h.get('quoteVolume', 0))
                                print(f"24小时涨幅: {price_change_percent:.2f}% (要求≥8%)")
                                print(f"24小时成交量: {quote_volume:.2f} USDT (要求≥1,000,000 USDT)")
                else:
                    print(f"{symbol} 不满足基本条件，跳过详细分析")

    except Exception as e:
        print(f"验证筛选逻辑时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_filtering()