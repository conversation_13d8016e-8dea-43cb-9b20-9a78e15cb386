{"exchange": "binance", "api_key": "nALsAPYzxprXmrgBRx7ZHIr1mnXlDqeZLoH3YL8hu8yu1Gpo65pHlJIDU6IlI20s", "api_secret": "HNBVAQJ4LwlnIuULhCquEG1CQnvYVPo2e0SSkrH1XYMZFdBMEdjvXgrL2QlLEN4u", "day_capital_usd": 20, "max_single_loss_usd": 3, "max_positions": 5, "capital": {"day_capital_usd": 20, "min_contract_balance": 100, "refill_amount": 50, "max_daily_loss_usd": 15}, "compounding": {"enabled": true, "withdraw_pct": 0.8, "max_open_pct": 0.2}, "stop_loss_pct": 3, "take_profit_pct": 50, "base_risk": {"enabled": true, "max_daily_loss_usd": 15}, "trade_session": {"start_hour": 9, "start_minute": 30, "end_hour": 23, "end_minute": 0}, "dynamic_window": false, "window_type": "both", "network": {"proxy": {"enabled": false, "host": "127.0.0.1", "port": 7897}}, "leverage": {"mode": "isolated", "target_leverage": 10, "cap_at_max": true}, "trading": {"max_coin_age_days": -1, "stop_loss_type": "atr", "fixed_stop_loss_pct": 3, "atr_multiplier": 3}, "advanced": {"enable_add": false, "enable_trail": false, "add_threshold_pct": 5, "trail_stop_pct": 3}, "logger": {"level": "INFO", "rich_output": true, "startup_banner": true, "show_proxy": true, "show_balance": true, "show_positions": true, "show_compound": true}}