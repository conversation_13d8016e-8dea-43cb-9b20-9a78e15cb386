#!/usr/bin/env python3
"""
ATR止损功能测试脚本
模拟策略运行，测试ATR止损计算和订单设置
"""

import json
import logging
from datetime import datetime, timedelta
import random

# 模拟交易数据
class MockTrader:
    def __init__(self):
        self.positions = {}
        self.orders = {}
        self.order_id = 1000
        
    def get_klines(self, symbol, interval='1d', limit=15):
        """模拟K线数据"""
        base_price = random.uniform(0.8, 1.2) * 100  # 基础价格
        volatility = random.uniform(0.02, 0.08)  # 波动率
        
        klines = []
        current_time = int(datetime.now().timestamp() * 1000)
        
        for i in range(limit):
            # 模拟价格波动
            price_change = random.gauss(0, volatility)
            open_price = base_price * (1 + price_change)
            high_price = open_price * (1 + random.uniform(0.01, 0.05))
            low_price = open_price * (1 - random.uniform(0.01, 0.05))
            close_price = random.uniform(low_price, high_price)
            volume = random.uniform(1000, 10000)
            
            kline = [
                current_time - (limit - i) * 86400000,  # 时间戳
                str(open_price),
                str(high_price),
                str(low_price),
                str(close_price),
                str(volume),
                current_time - (limit - i - 1) * 86400000,
                str(volume * 0.1),
                50,
                str(volume * 0.5),
                str(volume * 0.5)
            ]
            klines.append(kline)
            base_price = close_price
            
        return klines
    
    def place_order(self, symbol, side, order_type, quantity, stop_price=None, reduce_only=False):
        """模拟下单"""
        self.order_id += 1
        order = {
            'orderId': str(self.order_id),
            'symbol': symbol,
            'side': side,
            'type': order_type,
            'quantity': str(quantity),
            'status': 'NEW',
            'reduceOnly': reduce_only
        }
        if stop_price:
            order['stopPrice'] = str(stop_price)
            
        self.orders[self.order_id] = order
        return order
    
    def get_position(self, symbol):
        """模拟持仓查询"""
        return self.positions.get(symbol, None)

# 模拟策略
class MockATRStrategy:
    def __init__(self, config_path='config.json'):
        with open(config_path, 'r', encoding='utf-8') as f:
            self.cfg = json.load(f)
        
        self.trader = MockTrader()
        self.logger = logging.getLogger(__name__)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    def _calculate_atr_3x(self, symbol):
        """计算ATR 3倍值"""
        try:
            klines = self.trader.get_klines(symbol, interval='1d', limit=15)
            if not klines or len(klines) < 2:
                return 0
                
            # 计算真实波幅TR
            trs = []
            for i in range(1, len(klines)):
                high = float(klines[i][2])
                low = float(klines[i][3])
                prev_close = float(klines[i-1][4])
                
                # 真实波幅 = max(当日最高-最低, |当日最高-前日收盘|, |当日最低-前日收盘|)
                tr = max(high - low, abs(high - prev_close), abs(low - prev_close))
                trs.append(tr)
            
            if not trs:
                return 0
                
            # 计算ATR (简单平均)
            atr = sum(trs) / len(trs)
            current_price = float(klines[-1][4])
            atr_pct = (atr / current_price) * 100
            
            return atr_pct * 3  # 返回3倍ATR百分比
            
        except Exception as e:
            self.logger.error(f"计算ATR时出错: {e}")
            return 0
    
    def _place_stop_loss_order(self, symbol, quantity, entry_price):
        """根据配置的止损类型设置止损订单"""
        try:
            # 从配置文件获取止损类型和参数
            stop_loss_type = self.cfg.get('trading', {}).get('stop_loss_type', 'fixed')
            
            if stop_loss_type == "atr":
                # ATR止损模式
                atr_3x_pct = self._calculate_atr_3x(symbol)
                if atr_3x_pct <= 0:
                    self.logger.warning(f"{symbol} ATR计算失败，使用固定止损")
                    atr_3x_pct = self.cfg.get('trading', {}).get('fixed_stop_loss_pct', 3)
                else:
                    atr_multiplier = self.cfg.get('trading', {}).get('atr_multiplier', 3)
                    atr_3x_pct = atr_3x_pct * (atr_multiplier / 3)
                    
                stop_loss_pct = atr_3x_pct
                self.logger.info(f"{symbol} 使用ATR止损，ATR倍数: {atr_multiplier}x, 止损百分比: {stop_loss_pct:.2f}%")
            else:
                # 固定止损模式
                stop_loss_pct = self.cfg.get('trading', {}).get('fixed_stop_loss_pct', 3)
                self.logger.info(f"{symbol} 使用固定止损，止损百分比: {stop_loss_pct}%")
            
            # 计算止损价格
            stop_price = entry_price * (1 - stop_loss_pct / 100)
            
            # 模拟设置止损订单
            order = self.trader.place_order(
                symbol=symbol,
                side='SELL',
                order_type='STOP_MARKET',
                quantity=quantity,
                stop_price=stop_price,
                reduce_only=True
            )
            
            if order and 'orderId' in order:
                stop_order_id = order['orderId']
                self.logger.info(f"[✅止损设置成功] {symbol} 止损@{stop_price:.4f} ({stop_loss_pct:.2f}%), 订单ID: {stop_order_id}")
                return stop_order_id, stop_loss_pct, stop_price
            else:
                self.logger.error(f"[❌止损设置失败] {symbol}")
                return None, 0, 0
                
        except Exception as e:
            self.logger.error(f"设置止损订单时出错: {e}")
            return None, 0, 0
    
    def test_atr_calculation(self, symbol):
        """测试ATR计算"""
        self.logger.info(f"\n{'='*60}")
        self.logger.info(f"测试 {symbol} 的ATR计算")
        self.logger.info(f"{'='*60}")
        
        klines = self.trader.get_klines(symbol, limit=10)
        self.logger.info(f"获取到 {len(klines)} 根K线数据")
        
        # 显示最近几根K线数据
        for i, k in enumerate(klines[-3:]):
            timestamp = datetime.fromtimestamp(int(k[0])/1000).strftime('%Y-%m-%d')
            open_price = float(k[1])
            high_price = float(k[2])
            low_price = float(k[3])
            close_price = float(k[4])
            self.logger.info(f"K线{i+1}: {timestamp} O:{open_price:.4f} H:{high_price:.4f} L:{low_price:.4f} C:{close_price:.4f}")
        
        atr_3x = self._calculate_atr_3x(symbol)
        current_price = float(klines[-1][4])
        
        self.logger.info(f"当前价格: {current_price:.4f}")
        self.logger.info(f"3倍ATR百分比: {atr_3x:.2f}%")
        self.logger.info(f"ATR止损价格: {current_price * (1 - atr_3x/100):.4f}")
        
        return atr_3x
    
    def simulate_trading_scenarios(self):
        """模拟不同交易场景"""
        test_symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT', 'LINKUSDT']
        
        self.logger.info(f"\n{'='*80}")
        self.logger.info("🚀 开始ATR止损功能测试")
        self.logger.info(f"{'='*80}")
        
        results = []
        
        for symbol in test_symbols:
            self.logger.info(f"\n📊 测试 {symbol}")
            self.logger.info("-" * 50)
            
            # 测试ATR计算
            atr_pct = self.test_atr_calculation(symbol)
            
            # 模拟开仓
            entry_price = float(self.trader.get_klines(symbol)[-1][4])
            quantity = 0.1  # 模拟数量
            
            # 设置止损
            order_id, stop_pct, stop_price = self._place_stop_loss_order(symbol, quantity, entry_price)
            
            if order_id:
                results.append({
                    'symbol': symbol,
                    'entry_price': entry_price,
                    'atr_pct': atr_pct,
                    'stop_pct': stop_pct,
                    'stop_price': stop_price,
                    'order_id': order_id,
                    'status': 'success'
                })
            else:
                results.append({
                    'symbol': symbol,
                    'status': 'failed'
                })
        
        # 汇总结果
        self.logger.info(f"\n{'='*80}")
        self.logger.info("📋 测试结果汇总")
        self.logger.info(f"{'='*80}")
        
        success_count = sum(1 for r in results if r['status'] == 'success')
        total_count = len(results)
        
        self.logger.info(f"总测试数: {total_count}")
        self.logger.info(f"成功数: {success_count}")
        self.logger.info(f"失败数: {total_count - success_count}")
        
        for result in results:
            if result['status'] == 'success':
                self.logger.info(
                    f"✅ {result['symbol']}: "
                    f"入场价 {result['entry_price']:.4f} | "
                    f"ATR {result['atr_pct']:.2f}% | "
                    f"止损 {result['stop_pct']:.2f}% | "
                    f"止损价 {result['stop_price']:.4f}"
                )
            else:
                self.logger.info(f"❌ {result['symbol']}: 止损设置失败")
        
        return results

def main():
    """主测试函数"""
    print("🎯 开始ATR止损功能测试...")
    
    # 创建测试策略实例
    strategy = MockATRStrategy()
    
    # 运行测试
    results = strategy.simulate_trading_scenarios()
    
    # 检查配置
    stop_loss_type = strategy.cfg.get('trading', {}).get('stop_loss_type', 'fixed')
    atr_multiplier = strategy.cfg.get('trading', {}).get('atr_multiplier', 3)
    
    print(f"\n⚙️  当前配置:")
    print(f"   止损类型: {stop_loss_type}")
    print(f"   ATR倍数: {atr_multiplier}x")
    
    if stop_loss_type == "atr":
        print("✅ ATR止损已启用")
    else:
        print("⚠️  当前为固定止损模式")

if __name__ == "__main__":
    main()