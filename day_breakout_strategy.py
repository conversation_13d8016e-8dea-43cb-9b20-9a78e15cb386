#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DayBreakout 策略 5×杠杆·限价挂单·防追高版
直接覆盖原 day_breakout_strategy.py 即可运行
"""

import json, sys, os, time, logging
from datetime import datetime, timedelta
from decimal import Decimal
import numpy as np
import statistics
from rate_limit_proof import RateLimitProofTrader, L0Cache, RateLimiter

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class DayBreakoutStrategy:
    def __init__(self, trader, config):
        self.trader = trader
        self.cfg = config
        self.logger = logging.getLogger(__name__)
        self.decay_ref = datetime.utcnow()
        self.today_opened_usd = 0

        # RateLimit-Proof 架构
        self.rate_limit_trader = RateLimitProofTrader(trader)
        self.l0_cache = L0Cache(ttl_ms=1000)  # 1秒缓存
        self.rate_limiter = RateLimiter(max_weight=1150, window_s=60)

    # ---------- 工具 ----------
    def reset_daily(self):
        self.decay_ref = datetime.utcnow()
        self.today_opened_usd = 0
        self.logger.info("[OK] 北京00:00已重置")

    def _get_5m_klines(self, symbol, limit=21):
        """取5m K线，返回list[list]"""
        try:
            return self.trader.get_klines(symbol, interval='5m', limit=limit)
        except Exception as e:
            self.logger.error(f"5m K线获取失败 {symbol}: {e}")
            return []

    def _get_1m_klines(self, symbol, limit=21):
        """取1m K线，返回list[list]"""
        try:
            return self.trader.get_klines(symbol, interval='1m', limit=limit)
        except Exception as e:
            self.logger.error(f"1m K线获取失败 {symbol}: {e}")
            return []

    def _calculate_atr_1m(self, symbol):
        """计算1分钟ATR"""
        try:
            k1 = self._get_1m_klines(symbol, 21)
            if len(k1) < 2:
                return 0
            trs = []
            for i in range(1, len(k1)):
                h, l, c0 = float(k1[i][2]), float(k1[i][3]), float(k1[i-1][4])
                tr = max(h - l, abs(h - c0), abs(l - c0))
                trs.append(tr)
            atr = statistics.mean(trs) if trs else 0
            price = float(k1[-1][4])
            return atr
        except:
            return 0

    def _is_new_coin(self, symbol):
        max_age = self.cfg.get('trading', {}).get('max_coin_age_days', 90)
        if max_age == -1:
            return True
        try:
            info = self.trader.http.get('/fapi/v1/exchangeInfo')
            for s in info['symbols']:
                if s['symbol'] == symbol:
                    lt = s.get('listingTime', 0)
                    if lt:
                        days = (datetime.utcnow() - datetime.fromtimestamp(lt/1000)).days
                        return days <= max_age
            return True
        except:
            return True

    def _has_open_leverage(self, symbol):
        try:
            return symbol in self.trader.get_all_futures()
        except:
            return False

    def _calculate_atr_3x(self, symbol):
        try:
            k5 = self._get_5m_klines(symbol, 21)
            if len(k5) < 2:
                return 0
            trs = []
            for i in range(1, len(k5)):
                h, l, c0 = float(k5[i][2]), float(k5[i][3]), float(k5[i-1][4])
                tr = max(h - l, abs(h - c0), abs(l - c0))
                trs.append(tr)
            atr = statistics.mean(trs) if trs else 0
            price = float(k5[-1][4])
            return (atr / price) * 100 * 3
        except:
            return 0

    def _liquidation_price(self, symbol):
        """返回当前持仓强平价，无持仓返回0"""
        pos = self.trader.get_position(symbol)
        return float(pos['liquidationPrice']) if pos else 0

    def _current_position(self, symbol):
        """获取当前持仓 - RateLimit-Proof版本"""
        # 1. 先检查L0缓存
        cached_pos = self.l0_cache.get(f"pos_{symbol}")
        if cached_pos is not None:
            return cached_pos
            
        # 2. 检查WebSocket缓存
        ws_pos = self.rate_limit_trader.get_cached_position(symbol)
        if ws_pos:
            self.l0_cache.set(f"pos_{symbol}", ws_pos)
            return ws_pos
            
        # 3. 检查限速后请求REST（权重5）
        if self.rate_limiter.ask(5):
            try:
                positions = self.rate_limit_trader.get_positions(symbol)
                for pos in positions:
                    if pos['symbol'] == symbol and float(pos['positionAmt']) != 0:
                        self.l0_cache.set(f"pos_{symbol}", pos)
                        return pos
                self.l0_cache.set(f"pos_{symbol}", None)
                return None
            except Exception as e:
                self.logger.error(f"获取持仓失败 {symbol}: {e}")
                return None
        else:
            self.logger.warning(f"限速保护: 跳过持仓获取 {symbol}")
            return None

    def _current_lev(self, symbol):
        """获取当前实际杠杆倍数"""
        pos = self._current_position(symbol)
        if not pos:
            return 0
        notional = abs(float(pos['notional']))
        isolated_wallet = float(pos['isolatedWallet'])
        if isolated_wallet <= 0:
            return 0
        return notional / isolated_wallet

    def _reduce_only_down_to_lev(self, symbol, target_lev=2.0):
        """
        零追加自动降杆函数 - 只减仓，不加钱
        把杠杆压到 target_lev，通过减仓实现
        """
        pos = self.trader.get_position(symbol)
        if not pos:
            return
        
        curr_lev = abs(float(pos['notional'])) / float(pos['isolatedWallet'])
        if curr_lev <= target_lev:
            return  # 已达标

        curr_qty = abs(float(pos['positionAmt']))
        # 减仓数量 = 原Qty × (1 - 目标/当前)
        reduce_qty = curr_qty * (1 - target_lev / curr_lev)
        reduce_qty = round(reduce_qty, 3)
        if reduce_qty <= 0:
            return

        side = 'SELL' if float(pos['positionAmt']) > 0 else 'BUY'
        try:
            self.trader.place_order(
                symbol=symbol,
                side=side,
                order_type='MARKET',
                quantity=reduce_qty,
                reduce_only=True
            )
            self.logger.info(f"[零追加降杆] {symbol} 减={reduce_qty} 杠杆={curr_lev:.2f}→{target_lev}")
        except Exception as e:
            self.logger.error(f"[零追加降杆失败] {symbol}: {e}")

    def _place_limit_order_and_wait(self, symbol, side, qty, limit_price, timeout=300):
        """
        限价挂单，timeout秒内未完全成交则撤单返回None
        成功返回 (order dict, avg_price)
        """
        try:
            order = self.trader.place_order(
                symbol=symbol,
                side=side,
                order_type='LIMIT',
                quantity=qty,
                price=round(limit_price, 6),
                timeInForce='GTC'
            )
            if not order or 'orderId' not in order:
                return None, 0
            oid = order['orderId']
            t0 = time.time()
            while time.time() - t0 < timeout:
                time.sleep(1)
                sts = self.trader.http.get('/fapi/v1/order', {'symbol': symbol, 'orderId': oid})
                if sts and sts.get('status') == 'FILLED':
                    avg = float(sts.get('avgPrice', 0)) or float(sts.get('price', limit_price))
                    return sts, avg
                if sts and sts.get('status') in ['CANCELED', 'REJECTED']:
                    break
            # 未成交撤单
            self.trader.cancel_order(symbol, oid)
            return None, 0
        except Exception as e:
            self.logger.error(f"限价单异常 {symbol}: {e}")
            return None, 0

    # ---------- 核心过滤 ----------
    def _pass_sideways_filter(self, symbol):
        """
        20根5m压缩波动<2% 且 横盘时间>=10根
        """
        k5 = self._get_5m_klines(symbol, 21)
        if len(k5) < 21:
            return False
        highs = [float(x[2]) for x in k5[:-1]]  # 不含当前
        lows  = [float(x[3]) for x in k5[:-1]]
        rng = max(highs) - min(lows)
        mid = (max(highs) + min(lows)) / 2
        if rng / mid > 0.02:          # 波动>2% 放弃
            return False
        # 统计连续横盘>=10根
        flat = 0
        for i in range(-11, -1):      # 最近10根
            if abs(float(k5[i][4]) - mid) / mid <= 0.01:
                flat += 1
        return flat >= 8

    def _pass_percentile_filter(self, symbol):
        """
        当前价不得位于20根5m涨幅前25%分位
        """
        k5 = self._get_5m_klines(symbol, 21)
        if len(k5) < 21:
            return False
        changes = []
        for i in range(1, 21):
            c0, c1 = float(k5[i-1][4]), float(k5[i][4])
            changes.append((c1 - c0) / c0 * 100)
        curr_change = (float(k5[-1][4]) - float(k5[-2][4])) / float(k5[-2][4]) * 100
        q75 = np.percentile(changes, 75)
        return curr_change <= q75

    # ---------- 止损单（含强平保护） ----------
    def _place_stop_loss_order(self, symbol, qty, entry):
        """放置止损单 - RateLimit-Proof版本（预埋止损）"""
        try:
            atr_mult = self.cfg.get('trading', {}).get('atr_multiplier', 3)
            if self.cfg.get('trading', {}).get('stop_loss_type') == 'atr':
                sl_pct = self._calculate_atr_3x(symbol) / 3 * atr_mult
                if sl_pct <= 0:
                    sl_pct = self.cfg.get('trading', {}).get('fixed_stop_loss_pct', 3)
            else:
                sl_pct = self.cfg.get('trading', {}).get('fixed_stop_loss_pct', 3)

            liq = self._liquidation_price(symbol)
            if liq <= 0:
                liq = entry * 0.5
            # 理论止损价
            stop_price = entry * (1 - sl_pct / 100)
            # 强平保护
            min_safe = liq * 1.005
            if stop_price <= liq:
                sl_pct = ((entry - min_safe) / entry) * 100
                stop_price = min_safe
                self.logger.warning(
                    f"{symbol} 止损被上调至{stop_price:.4f}（{sl_pct:.2f}%）以远离强平价"
                )
            # 使用RateLimit-Proof交易器，权重=1
            if self.rate_limiter.ask(1):
                order = self.rate_limit_trader.place_stop_loss(
                    symbol=symbol,
                    qty=qty,
                    stop_price=round(stop_price, 6)
                )
                return order.get('orderId') if order else None
            else:
                self.logger.warning(f"限速保护: 跳过止损单设置 {symbol}")
                return None
        except Exception as e:
            self.logger.error(f"止损单失败 {symbol}: {e}")
            return None

    # ---------- 单币处理 ----------
    def process_symbol(self, symbol):
        try:
            # ---------- 0.  Weekend Filter ---------- 
            if self.cfg['trade_session'].get('weekend_filter', False): 
                utc = datetime.utcnow() 
                weekday = utc.weekday()          # 0=Monday 
                if weekday >= 5:                 # 5=Saturday 6=Sunday 
                    return False 

            # ---------- 1.  Liquidity Session Filter ---------- 
            sessions = self.cfg['trade_session']['sessions'] 
            now_min = datetime.utcnow().hour * 60 + datetime.utcnow().minute 
            in_any = False 
            for s in sessions: 
                start = s['start_hour'] * 60 + s['start_minute'] 
                end   = s['end_hour']   * 60 + s['end_minute'] 
                if end < start:                    # 跨夜 
                    if now_min < end or now_min >= start: 
                        in_any = True 
                        self.cfg['leverage']['target_leverage'] = s['max_leverage'] 
                        break 
                else: 
                    if start <= now_min < end: 
                        in_any = True 
                        self.cfg['leverage']['target_leverage'] = s['max_leverage'] 
                        break 
            if not in_any: 
                return False

            # 基础检查
            if not (self._is_new_coin(symbol) and self._has_open_leverage(symbol)):
                return False
            # 每日重置
            if datetime.utcnow() > self.decay_ref + timedelta(days=1):
                self.reset_daily()
            # 5m K线
            k5 = self._get_5m_klines(symbol, 21)
            if len(k5) < 21:
                return False
            highs = [float(x[2]) for x in k5[:-1]]
            lows  = [float(x[3]) for x in k5[:-1]]
            high, low = max(highs), min(lows)
            current = float(k5[-1][4])

            # 持仓管理
            pos = self.trader.get_position(symbol)
            pos_amt = float(pos['positionAmt']) if pos else 0
            if pos_amt != 0:
                ep = float(pos['entryPrice'])
                pnl = (current - ep) / ep * 100 * (1 if pos_amt > 0 else -1)
                
                # 零追加自动降杆检查
                target_lev = self.cfg.get('leverage', {}).get('target_leverage', 2.0)
                self._reduce_only_down_to_lev(symbol, target_lev)
                
                # 止损/止盈平仓
                if pnl <= -self.cfg['stop_loss_pct'] or pnl >= self.cfg['take_profit_pct']:
                    side = 'BUY' if pos_amt < 0 else 'SELL'
                    self.trader.place_order(symbol=symbol, side=side, order_type='MARKET',
                                          quantity=abs(pos_amt), reduce_only=True)
                    self.logger.info(f"[平仓] {symbol} @{current:.4f} PnL={pnl:.2f}%")
                return True

            # 突破方向（只做多）
            if not (current > high):
                return False

            # 过滤
            if not self._pass_sideways_filter(symbol):
                return False
            if not self._pass_percentile_filter(symbol):
                return False

            # 强平预检
            lev = self.cfg['leverage']['target_leverage']  # 使用配置的杠杆
            atr_sl = self._calculate_atr_3x(symbol) / 3 * self.cfg.get('trading', {}).get('atr_multiplier', 3)
            if atr_sl <= 0:
                atr_sl = self.cfg.get('trading', {}).get('fixed_stop_loss_pct', 3)
            theor_stop = current * (1 - atr_sl / 100)
            # 模拟强平价（粗略）
            liq_approx = current * (1 - 1 / lev * 0.9)  # 10%保证金
            if theor_stop <= liq_approx * 1.01:
                self.logger.debug(f"{symbol} 理论止损低于强平价，跳过")
                return False

            # 资金 & 限额
            capital_cfg = self.cfg['capital']
            max_open_usd = min(20, self.trader.get_total_balance() * 0.2, capital_cfg['day_capital_usd'])
            if self.today_opened_usd + max_open_usd > capital_cfg['day_capital_usd']:
                return False
            qty = max_open_usd / current
            if qty <= 0:
                return False

            # 设置杠杆
            if not self.trader.set_leverage(symbol, lev):
                return False

            # 限价挂单：突破价+0.2%
            limit_price = current * 1.002
            order, avg_price = self._place_limit_order_and_wait(symbol, 'BUY', qty, limit_price, timeout=300)
            if not order:
                # 价格回落保护
                ticker = self.trader.get_symbol_ticker(symbol)
                if ticker and float(ticker['price']) < current * 0.995:
                    self.logger.info(f"{symbol} 价格回落>0.5%，放弃追单")
                return False

            # 成交统计
            self.today_opened_usd += max_open_usd
            self.logger.info(
                f"[开仓成功] {symbol} 限价成交@{avg_price:.4f} 数量={qty} 金额={max_open_usd:.2f}U"
            )

            # 止损
            stop_id = self._place_stop_loss_order(symbol, qty, avg_price)
            if stop_id:
                self.logger.info(f"[止损] 订单ID={stop_id}")
            return True
        except Exception as e:
            self.logger.error(f"process_symbol {symbol}: {e}")
            return False

    # ---------- 主循环 ----------
    def run_forever(self):
        self.logger.info("[OK] 策略开始运行（5×杠杆·限价挂单·防追高）...")
        while True:
            try:
                futures = self.trader.get_all_futures()
                cnt = 0
                for s in futures:
                    if self.process_symbol(s):
                        cnt += 1
                    time.sleep(0.1)
                self.logger.info(f"本轮扫描完成，符合条件并挂单 {cnt} 个")
                time.sleep(60)
            except Exception as e:
                self.logger.error(f"run_forever: {e}")
                time.sleep(60)


# ---------------- 入口 ----------------
if __name__ == '__main__':
    from binance_trader import BinanceTrader  # 你的交易类
    logging.basicConfig(
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        level=logging.INFO
    )
    with open('config.json', 'r', encoding='utf-8') as f:
        cfg = json.load(f)
    trader = BinanceTrader(cfg)
    strategy = DayBreakoutStrategy(trader, cfg)
    strategy.run_forever()