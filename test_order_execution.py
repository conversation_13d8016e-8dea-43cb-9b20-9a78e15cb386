#!/usr/bin/env python3
"""
测试策略订单执行
"""

import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_order_execution():
    try:
        print("=== 测试策略订单执行 ===")
        
        # 导入必要的模块
        from day_breakout_strategy import DayBreakoutStrategy
        from binance_trader import BinanceTrader
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        
        # 创建策略实例
        strategy = DayBreakoutStrategy(trader, config)
        
        # 获取所有交易对
        all_futures = trader.get_all_futures()
        print(f"总共获取到 {len(all_futures)} 个交易对")
        
        # 选择一个交易对进行测试
        if all_futures:
            symbol = all_futures[0]
            print(f"\n测试交易对: {symbol}")
            
            # 检查是否为新币
            is_new = strategy._is_new_coin(symbol)
            print(f"是否为新币: {is_new}")
            
            # 检查是否已开放杠杆
            has_leverage = strategy._has_open_leverage(symbol)
            print(f"是否已开放杠杆: {has_leverage}")
            
            if is_new and has_leverage:
                print(f"\n{symbol} 满足基本条件，执行策略处理...")
                # 执行策略处理
                strategy.process_symbol(symbol)
                print("策略处理完成")
            else:
                print(f"\n{symbol} 不满足基本条件，跳过测试")
                
                # 尝试手动测试开仓逻辑
                print(f"\n手动测试 {symbol} 的开仓逻辑:")
                
                # 获取当前价格
                ticker = trader.get_symbol_ticker(symbol)
                if ticker and 'price' in ticker:
                    current_price = float(ticker['price'])
                    print(f"当前价格: {current_price}")
                    
                    # 计算开仓数量
                    total_balance = trader.get_total_balance()
                    print(f"总资金: {total_balance}")
                    
                    if total_balance > 0:
                        position_size = min(10, total_balance * 0.1) / current_price
                        print(f"计算开仓数量: {position_size}")
                        
                        # 尝试设置杠杆
                        print("尝试设置杠杆...")
                        if trader.set_leverage(symbol, 10):
                            print("杠杆设置成功")
                        else:
                            print("杠杆设置失败")
                    else:
                        print("资金不足，无法测试开仓")
                else:
                    print("无法获取当前价格")
        else:
            print("没有获取到交易对")
                
    except Exception as e:
        print(f"测试订单执行时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_order_execution()