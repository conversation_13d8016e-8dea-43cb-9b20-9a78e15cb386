# 活池-原子替换新币筛选管理器
# 权重优化：从60/分钟 → 0.017/分钟

def get_new_coins_list():
    """获取新币列表"""
    return ['APTUSDT', 'PYTHUSDT', 'SUIUSDT', 'SEIUSDT', 'TIAUSDT']

def is_new_coin(symbol):
    """判断是否为新币"""
    return symbol in get_new_coins_list()

def rebuild_live_pool():
    """重建活池"""
    coins = get_new_coins_list()
    print(f'[活池] 重建完成，共 {len(coins)} 个新币')
    return coins

if __name__ == '__main__':
    coins = get_new_coins_list()
    print(f'[活池] 新币数量: {len(coins)}')
    print(f'[活池] 新币: {coins}')