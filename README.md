# Day-Breakout 策略

这是一个专为加密货币市场设计的高性能交易策略,专注于捕捉日阳次日续涨的机会。

## 项目结构

```
day_breakout/
├── http_client.py          # HTTP客户端,处理API请求
├── binance_trader.py       # Binance交易接口
├── base_risk.py           # 风险控制模块
├── logger_config.py       # 日志配置
├── day_breakout_strategy.py # 核心策略
├── start_day_breakout.py  # 启动脚本
├── advanced_plugin.py     # 高级功能插件
├── monitor_strategy.py    # 策略监控
├── volatility_window.py   # 波动率计算
├── config.json           # 配置文件
└── docs/                 # 文档
    ├── TIME_WINDOW.md    # 时间窗口说明
    ├── TRADING_SESSIONS.md # 交易时段说明
    └── MAX_POSITIONS.md   # 持仓限制说明
```

## 功能特点

1. 多时段交易支持
   - 日盘交易(北京09:30-16:00)
   - 夜盘交易(北京21:30-04:00) 
   - 自适应时间窗口

2. 风险控制
   - 单笔最大损失限制
   - 每日最大损失限制
   - 最大持仓数量限制

3. 监控和日志
   - 完整的日志记录
   - 实时持仓监控
   - 性能指标统计

4. 高级特性
   - 波动率自适应
   - 智能止盈止损
   - 资金管理优化

## 使用说明

1. 配置文件设置
```bash
# 编辑配置文件
cp config.json.example config.json
vim config.json
```

2. 启动策略
```bash
python start_day_breakout.py
```

## 配置说明

1. API配置(config.json)
```json
{
  "api_key": "你的binance API key",
  "api_secret": "你的binance API secret",
  "test_net": false
}
```

2. 风险控制配置
```json
{
  "max_positions": 5,        # 最大持仓数
  "max_daily_loss": 1000,   # 每日最大亏损(USDT)
  "max_trade_loss": 100     # 单笔最大亏损(USDT)
}
```

3. 交易时段配置
```json
{
  "window_type": "day",     # day/night/both/adaptive
  "trade_session": {
    "start_hour": 1,        # UTC时间
    "start_minute": 30,
    "end_hour": 8,
    "end_minute": 0
  }
}
```

## 注意事项

1. 请确保API权限正确设置
2. 建议先在测试网进行测试
3. 定期检查日志和监控数据
4. 根据实际情况调整风控参数

## 更新历史

- 2025.09 
  - 优化时间窗口配置
  - 添加最大持仓限制
  - 完善监控功能

- 2025.08
  - 支持多时段交易
  - 增加风险控制参数
  - 改进日志系统

- 2025.07
  - 项目初始化
  - 基础功能实现