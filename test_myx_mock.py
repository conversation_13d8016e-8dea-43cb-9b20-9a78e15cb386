#!/usr/bin/env python3
"""
MYX币开仓和止损流程离线测试脚本
使用模拟数据测试开仓和止损逻辑，无需连接Binance API
"""

import json
import sys
import os
import time
import logging
from decimal import Decimal
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class MockBinanceTrader:
    """模拟BinanceTrader类"""
    
    def __init__(self, config):
        self.config = config
        self.order_counter = 1000
        self.orders = {}
        self.positions = {}
        
    def get_all_futures(self):
        """返回模拟的交易对列表"""
        return ["MYXUSDT", "BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT"]
    
    def get_symbol_ticker(self, symbol):
        """返回模拟的当前价格"""
        return {"symbol": symbol, "price": "0.001234"}
    
    def get_klines(self, symbol):
        """返回模拟的K线数据"""
        return [
            {"open": 0.001200, "high": 0.001250, "low": 0.001180, "close": 0.001234, "volume": 1000000},
            {"open": 0.001180, "high": 0.001220, "low": 0.001160, "close": 0.001200, "volume": 800000},
            {"open": 0.001220, "high": 0.001280, "low": 0.001180, "close": 0.001180, "volume": 1200000},
        ]
    
    def get_total_balance(self):
        """返回模拟的总资金"""
        return 1000.0
    
    def set_leverage(self, symbol, leverage):
        """模拟设置杠杆"""
        return True
    
    def place_order(self, symbol, side, order_type, quantity, stop_price=None, reduce_only=False):
        """模拟下单"""
        order_id = str(self.order_counter)
        self.order_counter += 1
        
        order = {
            "orderId": order_id,
            "symbol": symbol,
            "side": side,
            "type": order_type,
            "quantity": str(quantity),
            "status": "FILLED",
            "avgPrice": "0.001234"
        }
        
        if stop_price:
            order["stopPrice"] = str(stop_price)
        
        if reduce_only:
            order["reduceOnly"] = True
            
        self.orders[order_id] = order
        return order
    
    def get_order(self, symbol, order_id):
        """获取订单状态"""
        return self.orders.get(order_id, {"status": "NOT_FOUND"})
    
    def cancel_order(self, symbol, order_id):
        """模拟取消订单"""
        if order_id in self.orders:
            self.orders[order_id]["status"] = "CANCELED"
            return True
        return False

class MockDayBreakoutStrategy:
    """模拟DayBreakoutStrategy类"""
    
    def __init__(self, trader, config):
        self.trader = trader
        self.config = config
    
    def _is_new_coin(self, symbol):
        """模拟新币检查"""
        return True
    
    def _has_open_leverage(self, symbol):
        """模拟杠杆检查"""
        return True
    
    def _check_order_filled(self, symbol, order_id):
        """模拟订单成交检查"""
        if order_id in self.trader.orders:
            order = self.trader.orders[order_id]
            return True, float(order.get("quantity", 0)), float(order.get("avgPrice", 0))
        return False, 0, 0
    
    def process_symbol(self, symbol):
        """模拟完整的策略处理"""
        print(f"\n📊 开始处理 {symbol}...")
        
        # 检查基本条件
        if not self._is_new_coin(symbol):
            print(f"❌ {symbol} 不是新币")
            return False
        
        if not self._has_open_leverage(symbol):
            print(f"❌ {symbol} 杠杆未开放")
            return False
        
        # 获取价格和K线数据
        ticker = self.trader.get_symbol_ticker(symbol)
        current_price = float(ticker["price"])
        
        klines = self.trader.get_klines(symbol)
        if not klines:
            print(f"❌ 无法获取K线数据")
            return False
        
        # 计算开仓条件
        total_balance = self.trader.get_total_balance()
        max_open_usd = min(20, total_balance * 0.2)
        position_size = max_open_usd / current_price
        
        # 设置杠杆
        if not self.trader.set_leverage(symbol, 10):
            print(f"❌ 杠杆设置失败")
            return False
        
        # 开仓
        order = self.trader.place_order(
            symbol=symbol,
            side="BUY",
            order_type="MARKET",
            quantity=position_size
        )
        
        if not order or "orderId" not in order:
            print(f"❌ 开仓失败")
            return False
        
        open_order_id = order["orderId"]
        
        # 验证成交
        is_filled, filled_qty, avg_price = self._check_order_filled(symbol, open_order_id)
        if not is_filled or filled_qty <= 0:
            print(f"❌ 订单未成交，取消订单...")
            self.trader.cancel_order(symbol, open_order_id)
            return False
        
        # 设置止损
        stop_loss_pct = self.config["strategy"]["stop_loss_pct"]
        stop_price = avg_price * (1 - stop_loss_pct / 100)
        
        stop_order = self.trader.place_order(
            symbol=symbol,
            side="SELL",
            order_type="STOP_MARKET",
            quantity=filled_qty,
            stop_price=stop_price,
            reduce_only=True
        )
        
        if stop_order and "orderId" in stop_order:
            stop_order_id = stop_order["orderId"]
            print(f"✅ 开仓和止损设置成功")
            print(f"   开仓订单ID: {open_order_id}")
            print(f"   止损订单ID: {stop_order_id}")
            print(f"   开仓价格: {avg_price:.6f}")
            print(f"   止损价格: {stop_price:.6f}")
            print(f"   成交数量: {filled_qty}")
            return True
        else:
            print(f"❌ 止损设置失败")
            return False

def test_myx_mock_flow():
    """使用模拟数据测试MYX币开仓和止损流程"""
    try:
        print("=== MYX币开仓和止损流程离线测试 ===")
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('test_myx_mock.log'),
                logging.StreamHandler()
            ]
        )
        logger = logging.getLogger('test_myx_mock')
        
        # 模拟配置文件
        config = {
            "strategy": {
                "stop_loss_pct": 5.0,
                "max_open_positions": 5,
                "min_24h_price_change": 5.0,
                "min_24h_volume": 1000000
            }
        }
        
        # 创建模拟交易器
        trader = MockBinanceTrader(config)
        
        # 创建模拟策略
        strategy = MockDayBreakoutStrategy(trader, config)
        
        # 测试用的MYX币符号
        test_symbol = "MYXUSDT"
        
        print(f"\n1. 开始测试 {test_symbol} 的开仓和止损流程")
        logger.info(f"开始测试 {test_symbol} 的开仓和止损流程")
        
        # 步骤1：检查交易对是否存在
        print(f"\n2. 检查交易对 {test_symbol} 是否存在...")
        all_futures = trader.get_all_futures()
        
        if test_symbol not in all_futures:
            print(f"❌ {test_symbol} 不在交易对列表中")
            logger.error(f"{test_symbol} 不在交易对列表中")
            return False
        
        print(f"✅ {test_symbol} 存在于交易对列表中")
        
        # 步骤2：使用策略完整流程测试
        print(f"\n3. 执行策略完整流程测试...")
        
        success = strategy.process_symbol(test_symbol)
        
        if success:
            print(f"\n🎉 测试完成！MYX币开仓和止损流程验证成功")
            print("=" * 60)
            print(f"所有测试步骤均通过")
            print(f"开仓订单已创建")
            print(f"止损订单已设置")
            print(f"订单ID已正确返回")
            logger.info("MYX币开仓和止损流程测试成功")
            return True
        else:
            print(f"❌ 策略流程测试失败")
            logger.error("策略流程测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()
        logger.error(f"测试出错: {e}")
        return False

def test_order_flow_detailed():
    """详细测试订单流程的每个步骤"""
    try:
        print("\n=== 详细订单流程测试 ===")
        
        # 模拟配置文件
        config = {
            "strategy": {
                "stop_loss_pct": 5.0
            }
        }
        
        # 创建模拟交易器
        trader = MockBinanceTrader(config)
        
        # 测试用的MYX币符号
        test_symbol = "MYXUSDT"
        
        print(f"\n📋 测试步骤详情:")
        
        # 1. 获取价格
        ticker = trader.get_symbol_ticker(test_symbol)
        current_price = float(ticker["price"])
        print(f"1. 获取当前价格: {current_price}")
        
        # 2. 计算开仓数量
        total_balance = trader.get_total_balance()
        max_open_usd = min(20, total_balance * 0.2)
        position_size = max_open_usd / current_price
        print(f"2. 计算开仓数量: {position_size:.6f}")
        
        # 3. 设置杠杆
        leverage_result = trader.set_leverage(test_symbol, 10)
        print(f"3. 杠杆设置: {'✅ 成功' if leverage_result else '❌ 失败'}")
        
        # 4. 开仓
        order = trader.place_order(
            symbol=test_symbol,
            side="BUY",
            order_type="MARKET",
            quantity=position_size
        )
        
        if order and "orderId" in order:
            open_order_id = order["orderId"]
            print(f"4. 开仓订单: ✅ 成功")
            print(f"   订单ID: {open_order_id}")
            print(f"   成交价格: {order.get('avgPrice', current_price)}")
        else:
            print(f"4. 开仓订单: ❌ 失败")
            return False
        
        # 5. 验证成交
        order_status = trader.get_order(test_symbol, open_order_id)
        print(f"5. 订单状态: {order_status.get('status', 'UNKNOWN')}")
        
        # 6. 设置止损
        stop_loss_pct = config["strategy"]["stop_loss_pct"]
        stop_price = current_price * (1 - stop_loss_pct / 100)
        
        stop_order = trader.place_order(
            symbol=test_symbol,
            side="SELL",
            order_type="STOP_MARKET",
            quantity=position_size,
            stop_price=stop_price,
            reduce_only=True
        )
        
        if stop_order and "orderId" in stop_order:
            stop_order_id = stop_order["orderId"]
            print(f"6. 止损订单: ✅ 成功")
            print(f"   止损订单ID: {stop_order_id}")
            print(f"   止损价格: {stop_price:.6f}")
            print(f"   只减仓: {stop_order.get('reduceOnly', False)}")
            return True
        else:
            print(f"6. 止损订单: ❌ 失败")
            return False
            
    except Exception as e:
        print(f"❌ 详细测试出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 运行离线测试
    print("🚀 开始MYX币开仓和止损流程离线测试...")
    
    # 测试1：完整流程测试
    success1 = test_myx_mock_flow()
    
    # 测试2：详细步骤测试
    success2 = test_order_flow_detailed()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"完整流程测试: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"详细步骤测试: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("🎉 所有测试均通过！MYX币开仓和止损流程验证成功")
        print("\n📋 验证结果:")
        print("- 交易对存在检查 ✅")
        print("- 开仓订单创建 ✅")
        print("- 订单ID正确返回 ✅")
        print("- 止损订单设置 ✅")
        print("- 只减仓模式 ✅")
        print("- 订单状态验证 ✅")
    else:
        print("❌ 部分测试失败，请检查日志")
    
    print("\n📁 查看 test_myx_mock.log 获取详细日志")