#!/usr/bin/env python3
"""
验证策略逻辑是否正常工作
通过临时放宽筛选条件来确认策略能选出币并执行交易
"""

import json
import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')

def validate_strategy_logic():
    try:
        print("=== 验证策略逻辑 ===")
        
        # 导入必要的模块
        from day_breakout_strategy import DayBreakoutStrategy
        from binance_trader import BinanceTrader
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        
        # 创建策略实例
        strategy = DayBreakoutStrategy(trader, config)
        
        # 获取所有交易对
        all_futures = trader.get_all_futures()
        print(f"总共获取到 {len(all_futures)} 个交易对")
        
        # 临时放宽筛选条件来验证策略逻辑
        print("\n=== 临时放宽筛选条件进行测试 ===")
        
        # 统计信息
        processed_count = 0
        valid_count = 0
        
        # 分析前20个交易对
        test_symbols = all_futures[:20]
        
        for symbol in test_symbols:
            print(f"\n--- 分析 {symbol} ---")
            processed_count += 1
            
            # 1. 检查是否为新币（使用更宽松的条件）
            is_new = strategy._is_new_coin(symbol)
            print(f"是否为新币: {is_new}")
            
            # 2. 检查是否已开放杠杆
            has_leverage = strategy._has_open_leverage(symbol)
            print(f"是否已开放杠杆: {has_leverage}")
            
            # 3. 如果满足基本条件，继续检查其他条件
            if is_new and has_leverage:
                # 获取K线数据
                klines = trader.get_klines(symbol, interval='5m', limit=48)
                if not klines or len(klines) < 2:
                    print(f"无法获取足够的K线数据")
                    continue
                    
                print(f"获取到 {len(klines)} 根5分钟K线")
                
                # 4. 使用非常宽松的5分钟筛选条件
                recent_kline = klines[-1]  # 最近的K线
                prev_kline = klines[-2]    # 前一根K线
                
                recent_open = float(recent_kline[1])
                recent_close = float(recent_kline[4])
                recent_change_pct = ((recent_close - recent_open) / recent_open) * 100 if recent_open != 0 else 0
                
                recent_vol = float(recent_kline[5])
                prev_vol = float(prev_kline[5])
                vol_ratio = recent_vol / prev_vol if prev_vol > 0 else 0
                
                print(f"5分钟涨幅: {recent_change_pct:.2f}%")
                print(f"成交量比率: {vol_ratio:.2f}x")
                
                # 非常宽松的条件：只要有正涨幅和成交量就通过
                five_min_valid = recent_change_pct > 0 and recent_vol > 0
                print(f"5分钟筛选(宽松条件): {'通过' if five_min_valid else '未通过'}")
                
                if five_min_valid:
                    # 5. 获取当前价格和24小时统计信息
                    ticker = trader.get_symbol_ticker(symbol)
                    if not ticker or 'price' not in ticker:
                        print(f"无法获取当前价格")
                        continue
                        
                    current_price = float(ticker['price'])
                    print(f"当前价格: {current_price:.4f} USDT")
                    
                    # 宽松的价格条件
                    price_valid = current_price > 0.001  # 几乎所有币都满足
                    print(f"价格筛选(宽松条件): {'通过' if price_valid else '未通过'}")
                    
                    ticker_24h = trader.get_ticker(symbol)
                    if not ticker_24h:
                        print(f"无法获取24小时统计信息")
                        continue
                        
                    price_change_percent = float(ticker_24h.get('priceChangePercent', 0))
                    quote_volume = float(ticker_24h.get('quoteVolume', 0))
                    
                    print(f"24小时涨幅: {price_change_percent:.2f}%")
                    print(f"24小时成交量: {quote_volume:.2f} USDT")
                    
                    # 宽松的24小时条件
                    daily_valid = price_change_percent > -50  # 几乎所有币都满足
                    print(f"24小时筛选(宽松条件): {'通过' if daily_valid else '未通过'}")
                    
                    if price_valid and daily_valid:
                        # 6. 突破条件检查（使用更宽松的条件）
                        highs = [float(k[2]) for k in klines]
                        high = max(highs[:-1])  # 不包含当前K线
                        
                        print(f"前高: {high:.4f}, 当前价格: {current_price:.4f}")
                        
                        # 宽松的突破条件：只要价格高于前高就通过
                        breakout_valid = current_price > high
                        print(f"突破筛选(宽松条件): {'通过' if breakout_valid else '未通过'}")
                        
                        if breakout_valid:
                            valid_count += 1
                            print(f"★★★ {symbol} 符合所有宽松条件! ★★★")
                            
                            # 验证开仓逻辑（不实际下单，只验证逻辑）
                            print(f"\n验证 {symbol} 的开仓逻辑:")
                            
                            # 模拟获取持仓信息
                            curr_pos = trader.get_position(symbol)
                            pos_amt = float(curr_pos['positionAmt']) if curr_pos else 0
                            print(f"当前持仓: {pos_amt}")
                            
                            # 模拟获取总资金
                            total_balance = trader.get_total_balance()
                            print(f"总资金: {total_balance:.2f} USDT")
                            
                            # 模拟计算开仓数量
                            capital_cfg = config['capital']
                            max_open_usd = min(20, total_balance * 0.2, capital_cfg['day_capital_usd'])
                            position_size = max_open_usd / current_price if current_price > 0 else 0
                            print(f"计算开仓数量: {position_size:.6f}")
                            
                            # 检查是否超过最大持仓数
                            current_positions = trader.get_all_positions()
                            max_positions = config.get('max_positions', 5)
                            positions_count = len(current_positions)
                            print(f"当前持仓数: {positions_count}/{max_positions}")
                            
                            print(f"开仓逻辑验证: {'通过' if positions_count < max_positions else '未通过(持仓数超限)'}")
        
        print(f"\n=== 验证结果 ===")
        print(f"分析了 {processed_count} 个交易对")
        print(f"符合宽松条件的交易对: {valid_count}")
        
        if valid_count > 0:
            print("✓ 策略逻辑验证通过！策略能够选出币并执行交易逻辑。")
            print("✓ 可以将筛选条件调整回严格标准，策略仍能正常工作。")
        else:
            print("✗ 策略逻辑存在问题，需要进一步检查。")
                
    except Exception as e:
        print(f"验证策略逻辑时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    validate_strategy_logic()