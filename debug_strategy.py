#!/usr/bin/env python3
"""
调试策略筛选逻辑的脚本
"""

import json
import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')

def debug_strategy():
    try:
        # 导入必要的模块
        from day_breakout_strategy import DayBreakoutStrategy
        from binance_trader import BinanceTrader
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        
        # 创建策略实例
        strategy = DayBreakoutStrategy(trader, config)
        
        # 获取所有交易对
        all_futures = trader.get_all_futures()
        print(f"总共获取到 {len(all_futures)} 个交易对")
        
        # 选择几个交易对进行详细分析（不使用随机，选择前10个）
        test_symbols = all_futures[:10]
        
        print(f"选择 {len(test_symbols)} 个交易对进行详细分析:")
        
        valid_count = 0  # 记录符合条件的币数量
        
        for symbol in test_symbols:
            print(f"\n=== 分析 {symbol} ===")
            
            # 1. 检查是否为新币
            is_new = strategy._is_new_coin(symbol)
            print(f"是否为新币 (≤90天): {is_new}")
            
            # 2. 检查是否已开放杠杆
            has_leverage = strategy._has_open_leverage(symbol)
            print(f"是否已开放杠杆: {has_leverage}")
            
            # 如果不满足基本条件，跳过
            if not (is_new and has_leverage):
                print(f"{symbol} 不满足基本条件，跳过详细分析")
                continue
                
            # 3. 获取K线数据
            klines = trader.get_klines(symbol, interval='5m', limit=48)
            if not klines or len(klines) < 2:
                print(f"无法获取足够的 {symbol} 的K线数据")
                continue
                
            print(f"获取到 {len(klines)} 根5分钟K线")
            
            # 4. 5分钟筛选条件
            recent_kline = klines[-1]  # 最近的K线
            prev_kline = klines[-2]    # 前一根K线
            
            recent_open = float(recent_kline[1])
            recent_close = float(recent_kline[4])
            recent_change_pct = ((recent_close - recent_open) / recent_open) * 100 if recent_open != 0 else 0
            
            recent_vol = float(recent_kline[5])
            prev_vol = float(prev_kline[5])
            vol_ratio = recent_vol / prev_vol if prev_vol > 0 else 0
            
            print(f"5分钟涨幅: {recent_change_pct:.2f}% (要求≥3%)")
            print(f"成交量比率: {vol_ratio:.2f}x (要求≥1.5x)")
            
            # 检查5分钟条件
            if recent_change_pct < 3 or vol_ratio < 1.5:
                print(f"{symbol} 不满足5分钟筛选条件")
                continue
                    
            # 5. 获取当前价格和24小时统计信息
            ticker = trader.get_symbol_ticker(symbol)
            if not ticker or 'price' not in ticker:
                print(f"无法获取 {symbol} 的当前价格")
                continue
                
            current_price = float(ticker['price'])
            print(f"当前价格: {current_price:.4f} USDT (要求≤100 USDT)")
            
            if current_price > 100:
                print(f"{symbol} 价格超过100 USDT")
                continue
            
            ticker_24h = trader.get_ticker(symbol)
            if not ticker_24h:
                print(f"无法获取 {symbol} 的24小时统计信息")
                continue
                
            price_change_percent = float(ticker_24h.get('priceChangePercent', 0))
            quote_volume = float(ticker_24h.get('quoteVolume', 0))
            
            print(f"24小时涨幅: {price_change_percent:.2f}% (要求≥8%)")
            print(f"24小时成交量: {quote_volume:.2f} USDT (要求≥1,000,000 USDT)")
            
            # 检查24小时条件
            if price_change_percent < 8:
                print(f"{symbol} 24小时涨幅不足")
            if quote_volume < 1_000_000:
                print(f"{symbol} 24小时成交量不足")
                
            if price_change_percent >= 8 and quote_volume >= 1_000_000:
                print(f"{symbol} 满足24小时筛选条件")
            else:
                print(f"{symbol} 不满足24小时筛选条件")
                continue
                
            # 6. 突破条件检查
            highs = [float(k[2]) for k in klines]
            high = max(highs[:-1])  # 不包含当前K线
            
            print(f"前高: {high:.4f}, 当前价格: {current_price:.4f}")
            if current_price > high:
                print(f"{symbol} 满足突破条件")
                print(f"★★★ {symbol} 符合所有条件! ★★★")
                valid_count += 1
            else:
                print(f"{symbol} 不满足突破条件")
                
        print(f"\n=== 总结 ===")
        print(f"分析了 {len(test_symbols)} 个交易对，其中 {valid_count} 个符合所有条件")
                
    except Exception as e:
        print(f"调试策略时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_strategy()