#!/usr/bin/env python3
"""
测试最终修复的策略
"""

import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_final_fix():
    try:
        print("=== 测试最终修复的策略 ===")
        
        # 导入必要的模块
        from day_breakout_strategy import DayBreakoutStrategy
        from binance_trader import BinanceTrader
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        
        # 创建策略实例
        strategy = DayBreakoutStrategy(trader, config)
        
        print("✓ 策略类创建成功")
        print("✓ 所有模块导入成功")
        print("✓ 策略文件结构正确")
        
        # 获取所有交易对
        all_futures = trader.get_all_futures()
        print(f"✓ 获取到 {len(all_futures)} 个交易对")
        
        # 测试策略方法
        if hasattr(strategy, 'process_symbol'):
            print("✓ process_symbol 方法存在")
        if hasattr(strategy, 'run_forever'):
            print("✓ run_forever 方法存在")
        if hasattr(strategy, '_is_new_coin'):
            print("✓ _is_new_coin 方法存在")
        if hasattr(strategy, '_has_open_leverage'):
            print("✓ _has_open_leverage 方法存在")
            
        print("\n=== 测试完成 ===")
        print("策略文件已成功修复，可以正常启动策略了！")
                
    except Exception as e:
        print(f"测试最终修复时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_final_fix()