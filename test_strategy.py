#!/usr/bin/env python3
"""
测试策略逻辑的脚本
"""

import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_strategy():
    try:
        # 导入必要的模块
        from day_breakout_strategy import DayBreakoutStrategy
        from binance_trader import BinanceTrader
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        
        # 创建策略实例
        strategy = DayBreakoutStrategy(trader, config)
        
        # 获取一些交易对进行测试
        futures = trader.get_all_futures()
        test_symbols = futures[:5]  # 只测试前5个交易对
        
        print(f"测试交易对: {test_symbols}")
        
        # 测试处理单个交易对的逻辑
        for symbol in test_symbols:
            print(f"\n测试处理 {symbol}:")
            try:
                # 调用process_symbol方法
                strategy.process_symbol(symbol)
                print(f"  {symbol} 处理完成")
            except Exception as e:
                print(f"  {symbol} 处理出错: {e}")
                import traceback
                traceback.print_exc()
                
    except Exception as e:
        print(f"测试策略时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_strategy()