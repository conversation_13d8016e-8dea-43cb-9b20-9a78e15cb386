#!/usr/bin/env python3
"""
测试MYX币开仓和止损订单流程
模拟策略筛选出MYX币后的完整开仓和止损设置流程
"""

import json
import sys
import os
import time
import logging
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_myx_order_flow():
    """测试MYX币开仓和止损订单流程"""
    try:
        print("=== 测试MYX币开仓和止损订单流程 ===")
        
        # 导入必要的模块
        from day_breakout_strategy import DayBreakoutStrategy
        from binance_trader import BinanceTrader
        
        # 设置测试日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('test_myx_order_flow.log'),
                logging.StreamHandler()
            ]
        )
        logger = logging.getLogger('test_myx')
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        
        # 创建策略实例
        strategy = DayBreakoutStrategy(trader, config)
        
        # 测试用的MYX币符号
        test_symbol = "MYXUSDT"
        
        print(f"\n1. 开始测试 {test_symbol} 的开仓和止损流程")
        logger.info(f"开始测试 {test_symbol} 的开仓和止损流程")
        
        # 步骤1：检查交易对是否存在
        print(f"\n2. 检查交易对 {test_symbol} 是否存在...")
        all_futures = trader.get_all_futures()
        
        if test_symbol not in all_futures:
            print(f"❌ {test_symbol} 不在交易对列表中")
            logger.error(f"{test_symbol} 不在交易对列表中")
            
            # 显示前10个交易对供参考
            print("可用的交易对前10个:")
            for symbol in all_futures[:10]:
                print(f"  - {symbol}")
            return False
        
        print(f"✅ {test_symbol} 存在于交易对列表中")
        
        # 步骤2：检查是否为新币（模拟通过）
        print(f"\n3. 检查 {test_symbol} 是否为新币...")
        is_new = strategy._is_new_coin(test_symbol)
        print(f"新币检查结果: {is_new}")
        
        # 步骤3：检查杠杆是否开放
        print(f"\n4. 检查 {test_symbol} 杠杆是否开放...")
        has_leverage = strategy._has_open_leverage(test_symbol)
        print(f"杠杆开放结果: {has_leverage}")
        
        if not (is_new and has_leverage):
            print("❌ 不满足新币和杠杆条件")
            return False
        
        print("✅ 满足新币和杠杆条件")
        
        # 步骤4：获取当前价格和K线数据
        print(f"\n5. 获取 {test_symbol} 的当前价格和K线数据...")
        
        # 获取当前价格
        ticker = trader.get_symbol_ticker(test_symbol)
        if not ticker or 'price' not in ticker:
            print("❌ 无法获取当前价格")
            return False
        
        current_price = float(ticker['price'])
        print(f"✅ 当前价格: {current_price}")
        
        # 获取K线数据
        klines = trader.get_klines(test_symbol)
        if not klines or len(klines) < 10:
            print("❌ 无法获取K线数据")
            return False
        
        print(f"✅ 获取到 {len(klines)} 根K线数据")
        
        # 步骤5：模拟突破条件满足
        print(f"\n6. 模拟突破条件满足，准备开仓...")
        
        # 获取总资金
        total_balance = trader.get_total_balance()
        if total_balance <= 0:
            print("❌ 资金不足")
            return False
        
        print(f"✅ 总资金: {total_balance:.2f} USDT")
        
        # 计算开仓数量
        max_open_usd = min(20, total_balance * 0.2)
        position_size = max_open_usd / current_price
        
        print(f"计划开仓金额: {max_open_usd:.2f} USDT")
        print(f"计划开仓数量: {position_size:.6f}")
        
        # 步骤6：设置杠杆
        print(f"\n7. 设置 {test_symbol} 杠杆为 10 倍...")
        if not trader.set_leverage(test_symbol, 10):
            print("❌ 杠杆设置失败")
            return False
        
        print("✅ 杠杆设置成功")
        
        # 步骤7：执行开仓
        print(f"\n8. 执行开仓订单...")
        
        order = trader.place_order(
            symbol=test_symbol,
            side='BUY',
            order_type='MARKET',
            quantity=position_size
        )
        
        if not order or 'orderId' not in order:
            print("❌ 开仓订单失败")
            logger.error("开仓订单失败")
            return False
        
        open_order_id = order['orderId']
        print(f"✅ 开仓订单提交成功，订单ID: {open_order_id}")
        logger.info(f"开仓订单提交成功，订单ID: {open_order_id}")
        
        # 步骤8：验证订单成交
        print(f"\n9. 验证订单是否成交...")
        
        max_wait = 10
        for i in range(max_wait):
            is_filled, filled_qty, avg_price = strategy._check_order_filled(test_symbol, open_order_id)
            
            if is_filled and filled_qty > 0:
                print(f"✅ 订单已成交")
                print(f"   成交数量: {filled_qty}")
                print(f"   成交均价: {avg_price:.4f}")
                logger.info(f"订单已成交，成交数量: {filled_qty}, 成交均价: {avg_price:.4f}")
                break
            else:
                print(f"等待成交... ({i+1}/{max_wait})")
                time.sleep(1)
        else:
            print("❌ 订单未成交，取消订单")
            
            # 取消未成交订单
            if trader.cancel_order(test_symbol, open_order_id):
                print("✅ 未成交订单已取消")
            else:
                print("❌ 取消订单失败")
            
            return False
        
        # 步骤9：设置止损订单
        print(f"\n10. 设置止损订单...")
        
        stop_loss_pct = config['strategy']['stop_loss_pct']
        stop_price = avg_price * (1 - stop_loss_pct / 100)
        
        print(f"止损比例: {stop_loss_pct}%")
        print(f"止损价格: {stop_price:.4f}")
        
        stop_order = trader.place_order(
            symbol=test_symbol,
            side='SELL',
            order_type='STOP_MARKET',
            quantity=filled_qty,
            stop_price=stop_price,
            reduce_only=True
        )
        
        if stop_order and 'orderId' in stop_order:
            stop_order_id = stop_order['orderId']
            print(f"✅ 止损订单设置成功")
            print(f"   止损订单ID: {stop_order_id}")
            print(f"   止损价格: {stop_price:.4f}")
            print(f"   止损数量: {filled_qty}")
            logger.info(f"止损订单设置成功，订单ID: {stop_order_id}, 止损价格: {stop_price:.4f}, 数量: {filled_qty}")
        else:
            print("❌ 止损订单设置失败")
            logger.error("止损订单设置失败")
            return False
        
        # 步骤10：验证止损订单状态
        print(f"\n11. 验证止损订单状态...")
        
        time.sleep(2)  # 等待订单状态更新
        
        order_status = trader.get_order(test_symbol, stop_order_id)
        if order_status:
            status = order_status.get('status', 'UNKNOWN')
            print(f"✅ 止损订单状态: {status}")
            logger.info(f"止损订单状态: {status}")
        else:
            print("❌ 无法获取止损订单状态")
            return False
        
        print(f"\n🎉 测试完成！MYX币开仓和止损流程验证成功")
        print("=" * 50)
        print(f"开仓订单ID: {open_order_id}")
        print(f"止损订单ID: {stop_order_id}")
        print(f"开仓价格: {avg_price:.4f}")
        print(f"止损价格: {stop_price:.4f}")
        print(f"成交数量: {filled_qty}")
        
        logger.info("MYX币开仓和止损流程测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()
        
        # 使用全局logger
        logging.error(f"测试出错: {e}")
        return False

def test_myx_simulation():
    """模拟测试MYX币的完整策略流程"""
    try:
        print("\n=== 模拟测试MYX币策略流程 ===")
        
        from day_breakout_strategy import DayBreakoutStrategy
        from binance_trader import BinanceTrader
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        
        # 创建策略实例
        strategy = DayBreakoutStrategy(trader, config)
        
        # 模拟MYX币通过所有筛选条件
        test_symbol = "MYXUSDT"
        
        print(f"\n模拟 {test_symbol} 通过所有策略筛选条件...")
        
        # 检查交易对是否存在
        all_futures = trader.get_all_futures()
        if test_symbol in all_futures:
            print(f"✅ {test_symbol} 存在于交易对列表")
            
            # 使用策略的process_symbol方法测试完整流程
            print(f"\n执行策略处理 {test_symbol}...")
            result = strategy.process_symbol(test_symbol)
            
            if result:
                print(f"✅ 策略处理 {test_symbol} 成功")
            else:
                print(f"❌ 策略处理 {test_symbol} 失败")
                
        else:
            print(f"❌ {test_symbol} 不存在，使用第一个交易对测试")
            if all_futures:
                test_symbol = all_futures[0]
                print(f"使用 {test_symbol} 进行测试")
                result = strategy.process_symbol(test_symbol)
                
        return True
        
    except Exception as e:
        print(f"❌ 模拟测试出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 运行测试
    success = test_myx_order_flow()
    
    if not success:
        print("\n基本测试失败，尝试模拟测试...")
        test_myx_simulation()
    
    print("\n测试结束，请查看 test_myx_order_flow.log 获取详细日志")