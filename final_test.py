#!/usr/bin/env python3
"""
最终测试脚本，验证策略的所有功能
"""

import json
import sys
import os
from datetime import datetime, timedelta

# 确保在正确的目录下运行
os.chdir(os.path.dirname(os.path.abspath(__file__)))

def test_strategy_functionality():
    print("开始测试策略功能...")
    print(f"当前时间: {datetime.now()}")
    
    # 导入必要的模块
    try:
        from logger_config import setup_logger
        from binance_trader import BinanceTrader
        from day_breakout_strategy import DayBreakoutStrategy
        from base_risk import BaseRisk
        
        print("✅ 所有模块导入成功")
        
        # 测试配置文件读取
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        print("✅ 配置文件读取成功")
        
        # 验证配置文件结构
        required_keys = ['exchange', 'api_key', 'api_secret', 'day_capital_usd', 'max_single_loss_usd', 'base_risk', 'trade_session']
        for key in required_keys:
            if key not in config:
                print(f"❌ 配置文件缺少必要键: {key}")
                return False
        print("✅ 配置文件结构验证通过")
        
        # 测试交易时段配置
        trade_session = config['trade_session']
        required_session_keys = ['start_hour', 'start_minute', 'end_hour', 'end_minute']
        for key in required_session_keys:
            if key not in trade_session:
                print(f"❌ 交易时段配置缺少必要键: {key}")
                return False
        print("✅ 交易时段配置验证通过")
        
        # 测试策略类初始化
        # 创建一个模拟的trader对象
        class MockTrader:
            def __init__(self):
                # 模拟symbols属性
                self.symbols = {
                    'BTCUSDT': {'leverage': '20'},
                    'ETHUSDT': {'leverage': '20'}
                }
            
            def get_positions(self):
                return {}
            def get_all_tickers(self):
                return ['BTCUSDT', 'ETHUSDT']
            def get_klines(self, symbol, interval, limit):
                # 模拟48根K线数据
                return [['', '100', '', '', '105', '1000'] for _ in range(48)]
            def get_ticker(self, symbol):
                # 使用更合理的模拟价格，避免风控触发
                return {'lastPrice': '20000'}
            def create_order(self, symbol, side, qty):
                return {'status': 'FILLED'}
            def set_leverage(self, symbol, leverage):
                return {'leverage': leverage}
            def get_contract_balance(self):
                return 1000.0
            def get_total_balance(self):
                return 1000.0
            def transfer_from_spot(self, amount):
                return {'tranId': '12345'}
        
        mock_trader = MockTrader()
        
        # 测试策略初始化
        strategy = DayBreakoutStrategy(mock_trader, config)
        print("✅ 策略类初始化成功")
        
        # 测试重置每日参数
        strategy.reset_daily()
        print("✅ 重置每日参数成功")
        
        # 测试评分函数
        score = strategy.day_score('BTCUSDT')
        print(f"✅ 评分函数测试成功，BTCUSDT得分为: {score}")
        
        # 测试交易时段逻辑
        now = datetime.utcnow()
        trade_session = config.get('trade_session', {
            'start_hour': 1,
            'start_minute': 30,
            'end_hour': 8,
            'end_minute': 0
        })
        
        start_time = strategy.decay_ref + timedelta(
            hours=trade_session['start_hour'], 
            minutes=trade_session['start_minute']
        )
        end_time = strategy.decay_ref + timedelta(
            hours=trade_session['end_hour'], 
            minutes=trade_session['end_minute']
        )
        
        print(f"✅ 交易时段计算成功:")
        print(f"   开始时间: {start_time} (UTC)")
        print(f"   结束时间: {end_time} (UTC)")
        print(f"   当前时间: {now} (UTC)")
        
        # 测试开仓函数（不会真正下单）
        # 注意：测试中可能会触发风控，这是正常的
        strategy.open_position('BTCUSDT')
        print("✅ 开仓函数测试成功（即使触发风控也是正常的）")
        
        print("\n🎉 所有功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_strategy_functionality()
    if success:
        print("\n✅ 策略功能完整，可以正常运行！")
        sys.exit(0)
    else:
        print("\n❌ 策略功能测试失败，请检查错误信息")
        sys.exit(1)