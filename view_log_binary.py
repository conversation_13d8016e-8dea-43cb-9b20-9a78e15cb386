#!/usr/bin/env python3
"""
以二进制模式读取日志文件并尝试解码
"""

import os

def main():
    # 使用绝对路径
    log_file = r'd:\roll\day_breakout\day_breakout_final\day_breakout.log'
    
    if not os.path.exists(log_file):
        print(f"日志文件 {log_file} 不存在")
        return
        
    try:
        with open(log_file, 'rb') as f:
            content = f.read()
            print("文件大小:", len(content), "字节")
            
            if len(content) == 0:
                print("文件是空的")
                return
                
            print("前100字节:", content[:100])
            
            # 尝试不同的解码方式
            try:
                # 尝试UTF-8解码
                text = content.decode('utf-8')
                print("\n使用UTF-8解码成功:")
                lines = text.split('\n')
                for line in lines[-10:]:  # 显示最后10行
                    if line.strip():
                        print(line)
            except UnicodeDecodeError as e:
                print(f"\nUTF-8解码失败: {e}")
                
                # 尝试GBK解码
                try:
                    text = content.decode('gbk')
                    print("\n使用GBK解码成功:")
                    lines = text.split('\n')
                    for line in lines[-10:]:  # 显示最后10行
                        if line.strip():
                            print(line)
                except UnicodeDecodeError as e2:
                    print(f"\nGBK解码也失败: {e2}")
                    
                    # 尝试忽略错误的方式解码
                    try:
                        text = content.decode('utf-8', errors='ignore')
                        print("\n使用UTF-8忽略错误解码:")
                        lines = text.split('\n')
                        for line in lines[-10:]:  # 显示最后10行
                            if line.strip():
                                print(line)
                    except Exception as e3:
                        print(f"\n忽略错误解码也失败: {e3}")
                        
    except Exception as e:
        print(f"读取文件时出错: {e}")

if __name__ == '__main__':
    main()