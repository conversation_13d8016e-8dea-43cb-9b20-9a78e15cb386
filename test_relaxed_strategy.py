#!/usr/bin/env python3
"""
测试放宽条件的策略逻辑
"""

import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_relaxed_strategy():
    try:
        # 导入必要的模块
        from day_breakout_strategy import DayBreakoutStrategy
        from binance_trader import BinanceTrader
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        
        # 创建策略实例
        strategy = DayBreakoutStrategy(trader, config)
        
        # 获取所有交易对
        all_futures = trader.get_all_futures()
        print(f"总共获取到 {len(all_futures)} 个交易对")
        
        # 统计信息
        new_coins_count = 0
        has_leverage_count = 0
        price_filter_count = 0
        change_filter_count = 0
        volume_filter_count = 0
        five_min_filter_count = 0
        breakout_filter_count = 0
        
        # 分析前50个交易对
        test_symbols = all_futures[:50]
        
        for symbol in test_symbols:
            # 1. 检查是否为新币
            is_new = strategy._is_new_coin(symbol)
            if is_new:
                new_coins_count += 1
                
            # 2. 检查是否已开放杠杆
            has_leverage = strategy._has_open_leverage(symbol)
            if has_leverage:
                has_leverage_count += 1
                
            # 3. 如果满足基本条件，继续检查其他条件
            if is_new and has_leverage:
                print(f"\n分析 {symbol}:")
                
                # 获取K线数据
                klines = trader.get_klines(symbol, interval='5m', limit=48)
                if not klines or len(klines) < 2:
                    continue
                    
                # 4. 5分钟筛选条件
                recent_kline = klines[-1]  # 最近的K线
                prev_kline = klines[-2]    # 前一根K线
                
                recent_open = float(recent_kline[1])
                recent_close = float(recent_kline[4])
                recent_change_pct = ((recent_close - recent_open) / recent_open) * 100 if recent_open != 0 else 0
                
                recent_vol = float(recent_kline[5])
                prev_vol = float(prev_kline[5])
                vol_ratio = recent_vol / prev_vol if prev_vol > 0 else 0
                
                print(f"  5分钟涨幅: {recent_change_pct:.2f}% (要求≥3%)")
                print(f"  成交量比率: {vol_ratio:.2f}x (要求≥1.5x)")
                
                if recent_change_pct >= 3 and vol_ratio >= 1.5:
                    five_min_filter_count += 1
                    
                # 5. 获取当前价格和24小时统计信息
                ticker = trader.get_symbol_ticker(symbol)
                if not ticker or 'price' not in ticker:
                    continue
                    
                current_price = float(ticker['price'])
                print(f"  当前价格: {current_price:.4f} USDT (要求≤100 USDT)")
                
                if current_price <= 100:
                    price_filter_count += 1
                    
                ticker_24h = trader.get_ticker(symbol)
                if not ticker_24h:
                    continue
                    
                price_change_percent = float(ticker_24h.get('priceChangePercent', 0))
                quote_volume = float(ticker_24h.get('quoteVolume', 0))
                
                print(f"  24小时涨幅: {price_change_percent:.2f}% (要求≥8%)")
                print(f"  24小时成交量: {quote_volume:.2f} USDT (要求≥1,000,000 USDT)")
                
                if price_change_percent >= 8:
                    change_filter_count += 1
                    
                if quote_volume >= 1_000_000:
                    volume_filter_count += 1
                    
                # 6. 突破条件检查
                highs = [float(k[2]) for k in klines]
                high = max(highs[:-1])  # 不包含当前K线
                
                print(f"  前高: {high:.4f}, 当前价格: {current_price:.4f}")
                if current_price > high:
                    breakout_filter_count += 1
                    print(f"  ★★★ {symbol} 符合所有条件! ★★★")
        
        print(f"\n=== 统计结果 (分析了{len(test_symbols)}个交易对) ===")
        print(f"新币数量: {new_coins_count}")
        print(f"已开放杠杆数量: {has_leverage_count}")
        print(f"价格≤100 USDT数量: {price_filter_count}")
        print(f"24小时涨幅≥8%数量: {change_filter_count}")
        print(f"24小时成交量≥100万数量: {volume_filter_count}")
        print(f"5分钟筛选通过数量: {five_min_filter_count}")
        print(f"突破条件通过数量: {breakout_filter_count}")
        
        # 显示最严格的筛选条件
        print(f"\n=== 筛选条件分析 ===")
        print("最严格的筛选条件可能是:")
        filters = [
            ("新币", new_coins_count),
            ("已开放杠杆", has_leverage_count),
            ("价格≤100 USDT", price_filter_count),
            ("24小时涨幅≥8%", change_filter_count),
            ("24小时成交量≥100万", volume_filter_count),
            ("5分钟筛选", five_min_filter_count),
            ("突破条件", breakout_filter_count)
        ]
        
        min_filter = min(filters, key=lambda x: x[1])
        print(f"最严格的条件是: {min_filter[0]} (只有{min_filter[1]}个满足)")
                
    except Exception as e:
        print(f"测试放宽条件策略时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_relaxed_strategy()