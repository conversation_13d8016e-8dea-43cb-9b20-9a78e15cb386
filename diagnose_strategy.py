#!/usr/bin/env python3
"""
诊断策略问题
"""

import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def diagnose_strategy():
    try:
        print("=== 诊断策略问题 ===")
        
        # 导入必要的模块
        from day_breakout_strategy import DayBreakoutStrategy
        from binance_trader import BinanceTrader
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        
        # 创建策略实例
        strategy = DayBreakoutStrategy(trader, config)
        
        # 获取所有交易对
        all_futures = trader.get_all_futures()
        print(f"总共获取到 {len(all_futures)} 个交易对")
        
        # 选择一个交易对进行详细诊断
        if all_futures:
            symbol = all_futures[0]
            print(f"\n=== 诊断交易对: {symbol} ===")
            
            # 1. 检查是否为新币
            print("1. 检查是否为新币...")
            is_new = strategy._is_new_coin(symbol)
            print(f"   是否为新币 (≤90天): {is_new}")
            
            # 2. 检查是否已开放杠杆
            print("2. 检查是否已开放杠杆...")
            has_leverage = strategy._has_open_leverage(symbol)
            print(f"   是否已开放杠杆: {has_leverage}")
            
            # 3. 如果满足基本条件，继续检查
            if is_new and has_leverage:
                print("3. 满足基本条件，继续详细检查...")
                
                # 获取K线数据
                print("   获取K线数据...")
                klines = trader.get_klines(symbol, interval='5m', limit=48)
                print(f"   获取到 {len(klines)} 根5分钟K线")
                
                if len(klines) >= 2:
                    # 计算5分钟筛选条件
                    recent_kline = klines[-1]  # 最近的K线
                    prev_kline = klines[-2]    # 前一根K线
                    
                    recent_open = float(recent_kline[1])
                    recent_close = float(recent_kline[4])
                    recent_change_pct = ((recent_close - recent_open) / recent_open) * 100
                    
                    recent_vol = float(recent_kline[5])
                    prev_vol = float(prev_kline[5])
                    vol_ratio = recent_vol / prev_vol if prev_vol > 0 else 0
                    
                    print(f"   5分钟涨幅: {recent_change_pct:.2f}% (要求≥3%)")
                    print(f"   成交量比率: {vol_ratio:.2f}x (要求≥1.5x)")
                    
                    # 检查5分钟条件
                    five_min_pass = recent_change_pct >= 3 and vol_ratio >= 1.5
                    print(f"   5分钟筛选通过: {five_min_pass}")
                    
                    if five_min_pass:
                        # 获取当前价格和24小时统计信息
                        print("   获取当前价格...")
                        ticker = trader.get_symbol_ticker(symbol)
                        if ticker and 'price' in ticker:
                            current_price = float(ticker['price'])
                            print(f"   当前价格: {current_price:.4f} USDT (要求≤100 USDT)")
                            
                            print("   获取24小时统计...")
                            ticker_24h = trader.get_ticker(symbol)
                            if ticker_24h:
                                price_change_percent = float(ticker_24h.get('priceChangePercent', 0))
                                quote_volume = float(ticker_24h.get('quoteVolume', 0))
                                
                                print(f"   24小时涨幅: {price_change_percent:.2f}% (要求≥8%)")
                                print(f"   24小时成交量: {quote_volume:.2f} USDT (要求≥1,000,000 USDT)")
                                
                                # 检查价格和24小时条件
                                price_pass = current_price <= 100
                                change_pass = price_change_percent >= 8
                                volume_pass = quote_volume >= 1_000_000
                                
                                print(f"   价格条件通过: {price_pass}")
                                print(f"   涨幅条件通过: {change_pass}")
                                print(f"   成交量条件通过: {volume_pass}")
                                
                                if price_pass and change_pass and volume_pass:
                                    # 检查突破条件
                                    highs = [float(k[2]) for k in klines]
                                    high = max(highs[:-1])  # 不包含当前K线
                                    
                                    print(f"   前高: {high:.4f}, 当前价格: {current_price:.4f}")
                                    breakout_pass = current_price > high
                                    print(f"   突破条件通过: {breakout_pass}")
                                    
                                    if breakout_pass:
                                        print("   ✓ 所有条件通过，应该会开仓!")
                                        
                                        # 检查持仓情况
                                        print("   检查当前持仓...")
                                        curr_pos = trader.get_position(symbol)
                                        pos_amt = float(curr_pos['positionAmt']) if curr_pos else 0
                                        print(f"   当前持仓: {pos_amt}")
                                        
                                        if pos_amt == 0:
                                            print("   没有持仓，应该会开仓...")
                                        else:
                                            print("   已有持仓，不会开新仓")
                                    else:
                                        print("   ✗ 突破条件未通过")
                                else:
                                    print("   ✗ 24小时筛选条件未通过")
                            else:
                                print("   ✗ 无法获取24小时统计信息")
                        else:
                            print("   ✗ 无法获取当前价格")
                    else:
                        print("   ✗ 5分钟筛选条件未通过")
                else:
                    print("   ✗ K线数据不足")
            else:
                print("   ✗ 不满足基本条件")
                
        else:
            print("没有获取到交易对")
            
        print("\n=== 诊断完成 ===")
                
    except Exception as e:
        print(f"诊断策略时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    diagnose_strategy()