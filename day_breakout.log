2025-09-09 16:32:14 [INFO] 配置文件加载完成，代理设置: {'enabled': False, 'host': '127.0.0.1', 'port': 7897}
2025-09-09 16:32:16 [ERROR] 直连测试失败: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/time (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000002640F352F50>, 'Connection to fapi.binance.com timed out. (connect timeout=2)'))
2025-09-09 16:32:16 [INFO] 直连测试失败，启用代理
2025-09-09 16:32:16 [INFO] 自动代理设置完成，代理状态: True
2025-09-09 16:32:17 [INFO] Day-Breakout Strategy Starting...
2025-09-09 16:32:17 [INFO] 账户余额: 151.56 USDT
2025-09-09 16:32:17 [INFO] 交易时段: 09:30 - 23:00
2025-09-09 16:32:17 [INFO] 复利模式: 已启用 (每日提取80%, 最大开仓20%)
2025-09-09 16:32:17 [INFO] [OK] 复利已禁用
2025-09-09 16:33:02 [INFO] 配置文件加载完成，代理设置: {'enabled': False, 'host': '127.0.0.1', 'port': 7897}
2025-09-09 16:33:04 [ERROR] 直连测试失败: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/time (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x00000218503F2E10>, 'Connection to fapi.binance.com timed out. (connect timeout=2)'))
2025-09-09 16:33:04 [INFO] 直连测试失败，启用代理
2025-09-09 16:33:04 [INFO] 自动代理设置完成，代理状态: True
2025-09-09 16:33:05 [INFO] Day-Breakout Strategy Starting...
2025-09-09 16:33:05 [INFO] 账户余额: 151.56 USDT
2025-09-09 16:33:05 [INFO] 交易时段: 09:30 - 23:00
2025-09-09 16:33:05 [INFO] 复利模式: 已启用 (每日提取80%, 最大开仓20%)
2025-09-09 16:33:05 [INFO] [OK] 复利已禁用
2025-09-09 18:33:16 [INFO] 配置文件加载完成，代理设置: {'enabled': False, 'host': '127.0.0.1', 'port': 7897}
2025-09-09 18:33:19 [ERROR] 直连测试失败: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/time (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x00000222876F2F90>, 'Connection to fapi.binance.com timed out. (connect timeout=2)'))
2025-09-09 18:33:19 [INFO] 直连测试失败，启用代理
2025-09-09 18:33:19 [INFO] 自动代理设置完成，代理状态: True
2025-09-09 18:33:20 [INFO] Day-Breakout Strategy Starting...
2025-09-09 18:33:21 [INFO] 账户余额: 151.56 USDT
2025-09-09 18:33:21 [INFO] 交易时段: 09:30 - 23:00
2025-09-09 18:33:21 [INFO] 复利模式: 已启用 (每日提取80%, 最大开仓20%)
2025-09-09 18:33:21 [INFO] [OK] 复利已禁用
2025-09-09 18:47:20 [INFO] 配置文件加载完成，代理设置: {'enabled': False, 'host': '127.0.0.1', 'port': 7897}
2025-09-09 18:47:22 [ERROR] 直连测试失败: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/time (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x0000022E6A013490>, 'Connection to fapi.binance.com timed out. (connect timeout=2)'))
2025-09-09 18:47:22 [INFO] 直连测试失败，启用代理
2025-09-09 18:47:22 [INFO] 自动代理设置完成，代理状态: True
2025-09-09 18:47:25 [INFO] Day-Breakout Strategy Starting...
2025-09-09 18:47:26 [INFO] 账户余额: 151.56 USDT
2025-09-09 18:47:26 [INFO] 交易时段: 09:30 - 23:00
2025-09-09 18:47:26 [INFO] 复利模式: 已启用 (每日提取80%, 最大开仓20%)
2025-09-09 18:47:26 [INFO] [OK] 复利已禁用
2025-09-09 19:04:53 [INFO] 配置文件加载完成，代理设置: {'enabled': False, 'host': '127.0.0.1', 'port': 7897}
2025-09-09 19:04:53 [INFO] 直连测试成功，禁用代理
2025-09-09 19:04:53 [INFO] 自动代理设置完成，代理状态: False
2025-09-09 19:04:53 [INFO] Day-Breakout Strategy Starting...
2025-09-09 19:04:53 [INFO] 账户余额: 151.56 USDT
2025-09-09 19:04:53 [INFO] 交易时段: 09:30 - 23:00
2025-09-09 19:04:53 [INFO] 复利模式: 已启用 (每日提取80%, 最大开仓20%)
2025-09-09 19:04:53 [INFO] [OK] 复利已禁用
2025-09-09 22:37:00 [INFO] 配置文件加载完成，代理设置: {'enabled': False, 'host': '127.0.0.1', 'port': 7897}
2025-09-09 22:37:00 [INFO] 直连测试成功，禁用代理
2025-09-09 22:37:00 [INFO] 自动代理设置完成，代理状态: False
2025-09-09 22:37:01 [INFO] Day-Breakout Strategy Starting...
2025-09-09 22:37:01 [INFO] 账户余额: 151.56 USDT
2025-09-09 22:37:01 [INFO] 交易时段: 09:30 - 23:00
2025-09-09 22:37:01 [INFO] 复利模式: 已启用 (每日提取80%, 最大开仓20%)
2025-09-09 22:37:01 [INFO] [OK] 复利已禁用
2025-09-10 19:22:19 [INFO] 配置文件加载完成，代理设置: {'enabled': False, 'host': '127.0.0.1', 'port': 7897}
2025-09-10 19:22:21 [ERROR] 直连测试失败: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/time (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x0000020CAA353590>, 'Connection to fapi.binance.com timed out. (connect timeout=2)'))
2025-09-10 19:22:21 [INFO] 直连测试失败，启用代理
2025-09-10 19:22:21 [INFO] 自动代理设置完成，代理状态: True
2025-09-10 19:22:24 [INFO] Day-Breakout Strategy Starting...
2025-09-10 19:22:24 [INFO] 账户余额: 150.96 USDT
2025-09-10 19:22:24 [INFO] 交易时段: 09:30 - 23:00
2025-09-10 19:22:24 [INFO] 复利模式: 已启用 (每日提取80%, 最大开仓20%)
2025-09-10 19:22:24 [INFO] [OK] 复利已禁用
2025-09-10 22:45:12 [INFO] 配置文件加载完成，代理设置: {'enabled': False, 'host': '127.0.0.1', 'port': 7897}
2025-09-10 22:45:14 [ERROR] 直连测试失败: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/time (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x00000145B6677790>, 'Connection to fapi.binance.com timed out. (connect timeout=2)'))
2025-09-10 22:45:14 [INFO] 直连测试失败，启用代理
2025-09-10 22:45:14 [INFO] 自动代理设置完成，代理状态: True
2025-09-10 22:45:16 [INFO] Day-Breakout Strategy Starting...
2025-09-10 22:45:17 [INFO] 账户余额: 148.98 USDT
2025-09-10 22:47:29 [INFO] 配置文件加载完成，代理设置: {'enabled': False, 'host': '127.0.0.1', 'port': 7897}
2025-09-10 22:47:31 [ERROR] 直连测试失败: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/time (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001FC70516B50>, 'Connection to fapi.binance.com timed out. (connect timeout=2)'))
2025-09-10 22:47:31 [INFO] 直连测试失败，启用代理
2025-09-10 22:47:31 [INFO] 自动代理设置完成，代理状态: True
2025-09-10 22:47:32 [INFO] Day-Breakout Strategy Starting...
2025-09-10 22:47:33 [INFO] 账户余额: 148.98 USDT
2025-09-10 22:47:33 [INFO] 交易时段: Asia Range, London Break, NY Momentum
2025-09-10 22:47:33 [INFO]   - Asia Range: 00:00-04:00 (杠杆3x)
2025-09-10 22:47:33 [INFO]   - London Break: 08:00-13:30 (杠杆5x)
2025-09-10 22:47:33 [INFO]   - NY Momentum: 13:30-22:00 (杠杆7x)
2025-09-10 22:47:33 [INFO] 周末过滤: 已启用
2025-09-10 22:47:33 [INFO] 复利模式: 已启用 (每日提取80%, 最大开仓20%)
2025-09-10 22:47:33 [INFO] 当前杠杆: 5x
2025-09-10 22:47:33 [INFO] [OK] 复利已禁用
2025-09-10 22:49:30 [INFO] 配置文件加载完成，代理设置: {'enabled': False, 'host': '127.0.0.1', 'port': 7897}
2025-09-10 22:49:32 [ERROR] 直连测试失败: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/time (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x00000259211D7110>, 'Connection to fapi.binance.com timed out. (connect timeout=2)'))
2025-09-10 22:49:32 [INFO] 直连测试失败，启用代理
2025-09-10 22:49:32 [INFO] 自动代理设置完成，代理状态: True
2025-09-10 22:49:34 [INFO] Day-Breakout Strategy Starting...
2025-09-10 22:49:34 [INFO] 账户余额: 148.98 USDT
2025-09-10 22:49:34 [INFO] 交易时段: Asia Range, London Break, NY Momentum
2025-09-10 22:49:34 [INFO]   - Asia Range: 00:00-04:00 (杠杆3x)
2025-09-10 22:49:34 [INFO]   - London Break: 08:00-13:30 (杠杆5x)
2025-09-10 22:49:34 [INFO]   - NY Momentum: 13:30-22:00 (杠杆7x)
2025-09-10 22:49:34 [INFO] 周末过滤: 已启用
2025-09-10 22:49:34 [INFO] 复利模式: 已启用 (每日提取80%, 最大开仓20%)
2025-09-10 22:49:34 [INFO] 当前杠杆: 5x
2025-09-10 22:49:34 [INFO] [OK] 复利已禁用
2025-09-10 22:55:16 [INFO] 配置文件加载完成，代理设置: {'enabled': False, 'host': '127.0.0.1', 'port': 7897}
2025-09-10 22:55:18 [ERROR] 直连测试失败: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/time (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001C006A66ED0>, 'Connection to fapi.binance.com timed out. (connect timeout=2)'))
2025-09-10 22:55:18 [INFO] 直连测试失败，启用代理
2025-09-10 22:55:18 [INFO] 自动代理设置完成，代理状态: True
2025-09-10 22:55:19 [INFO] Day-Breakout Strategy Starting...
2025-09-10 22:55:19 [INFO] 账户余额: 148.98 USDT
2025-09-10 22:55:19 [INFO] 交易时段: Asia Range, London Break, NY Momentum
2025-09-10 22:55:19 [INFO]   - Asia Range: 00:00-04:00 (杠杆3x)
2025-09-10 22:55:19 [INFO]   - London Break: 08:00-13:30 (杠杆5x)
2025-09-10 22:55:19 [INFO]   - NY Momentum: 13:30-22:00 (杠杆7x)
2025-09-10 22:55:19 [INFO] 周末过滤: 已启用
2025-09-10 22:55:19 [INFO] 复利模式: 已启用 (每日提取80%, 最大开仓20%)
2025-09-10 22:55:19 [INFO] 当前杠杆: 5x
2025-09-10 22:55:19 [INFO] [OK] 复利已禁用
2025-09-10 23:09:54 [INFO] 配置文件加载完成，代理设置: {'enabled': False, 'host': '127.0.0.1', 'port': 7897}
2025-09-10 23:09:56 [ERROR] 直连测试失败: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/time (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000002002F016F10>, 'Connection to fapi.binance.com timed out. (connect timeout=2)'))
2025-09-10 23:09:56 [INFO] 直连测试失败，启用代理
2025-09-10 23:09:56 [INFO] 自动代理设置完成，代理状态: True
2025-09-10 23:09:57 [INFO] Day-Breakout Strategy Starting...
2025-09-10 23:09:58 [INFO] 账户余额: 147.66 USDT
2025-09-10 23:09:58 [INFO] 交易时段: Asia Range, London Break, NY Momentum
2025-09-10 23:09:58 [INFO]   - Asia Range: 00:00-04:00 (杠杆3x)
2025-09-10 23:09:58 [INFO]   - London Break: 08:00-13:30 (杠杆5x)
2025-09-10 23:09:58 [INFO]   - NY Momentum: 13:30-22:00 (杠杆7x)
2025-09-10 23:09:58 [INFO] 周末过滤: 已启用
2025-09-10 23:09:58 [INFO] 复利模式: 已启用 (每日提取80%, 最大开仓20%)
2025-09-10 23:09:58 [INFO] 当前杠杆: 5x
2025-09-10 23:09:58 [INFO] [OK] 复利已禁用
2025-09-11 00:22:39 [INFO] 配置文件加载完成，代理设置: {'enabled': False, 'host': '127.0.0.1', 'port': 7897}
2025-09-11 00:22:41 [ERROR] 直连测试失败: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/time (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001FA2A086BD0>, 'Connection to fapi.binance.com timed out. (connect timeout=2)'))
2025-09-11 00:22:41 [INFO] 直连测试失败，启用代理
2025-09-11 00:22:41 [INFO] 自动代理设置完成，代理状态: True
2025-09-11 00:22:42 [INFO] Day-Breakout Strategy Starting...
2025-09-11 00:22:42 [INFO] 账户余额: 147.41 USDT
2025-09-11 00:22:42 [INFO] 交易时段: Asia Range, London Break, NY Momentum
2025-09-11 00:22:42 [INFO]   - Asia Range: 00:00-04:00 (杠杆3x)
2025-09-11 00:22:42 [INFO]   - London Break: 08:00-13:30 (杠杆5x)
2025-09-11 00:22:42 [INFO]   - NY Momentum: 13:30-22:00 (杠杆7x)
2025-09-11 00:22:42 [INFO] 周末过滤: 已启用
2025-09-11 00:22:42 [INFO] 复利模式: 已启用 (每日提取80%, 最大开仓20%)
2025-09-11 00:22:42 [INFO] 当前杠杆: 5x
2025-09-11 00:22:42 [INFO] [OK] 复利已禁用
2025-09-11 00:31:15 [INFO] 配置文件加载完成，代理设置: {'enabled': False, 'host': '127.0.0.1', 'port': 7897}
2025-09-11 00:31:15 [INFO] 直连测试成功，禁用代理
2025-09-11 00:31:15 [INFO] 自动代理设置完成，代理状态: False
2025-09-11 00:31:16 [INFO] Day-Breakout Strategy Starting...
2025-09-11 00:31:16 [INFO] 账户余额: 147.41 USDT
2025-09-11 00:31:16 [INFO] 交易时段: Asia Range, London Break, NY Momentum
2025-09-11 00:31:16 [INFO]   - Asia Range: 00:00-04:00 (杠杆3x)
2025-09-11 00:31:16 [INFO]   - London Break: 08:00-13:30 (杠杆5x)
2025-09-11 00:31:16 [INFO]   - NY Momentum: 13:30-22:00 (杠杆7x)
2025-09-11 00:31:16 [INFO] 周末过滤: 已启用
2025-09-11 00:31:16 [INFO] 复利模式: 已启用 (每日提取80%, 最大开仓20%)
2025-09-11 00:31:16 [INFO] 当前杠杆: 5x
2025-09-11 00:31:16 [INFO] [OK] 复利已禁用
2025-09-11 00:32:33 [INFO] 配置文件加载完成，代理设置: {'enabled': False, 'host': '127.0.0.1', 'port': 7897}
2025-09-11 00:32:33 [INFO] 直连测试成功，禁用代理
2025-09-11 00:32:33 [INFO] 自动代理设置完成，代理状态: False
2025-09-11 00:32:33 [INFO] Day-Breakout Strategy Starting...
2025-09-11 00:32:33 [INFO] 账户余额: 147.40 USDT
2025-09-11 00:32:33 [INFO] 交易时段: Asia Range, London Break, NY Momentum
2025-09-11 00:32:33 [INFO]   - Asia Range: 00:00-04:00 (杠杆3x)
2025-09-11 00:32:33 [INFO]   - London Break: 08:00-13:30 (杠杆5x)
2025-09-11 00:32:33 [INFO]   - NY Momentum: 13:30-22:00 (杠杆7x)
2025-09-11 00:32:33 [INFO] 周末过滤: 已启用
2025-09-11 00:32:33 [INFO] 复利模式: 已启用 (每日提取80%, 最大开仓20%)
2025-09-11 00:32:33 [INFO] 当前杠杆: 5x
2025-09-11 00:32:33 [INFO] [OK] 复利已禁用
2025-09-11 07:06:29 [INFO] 配置文件加载完成，代理设置: {'enabled': False, 'host': '127.0.0.1', 'port': 7897}
2025-09-11 07:06:31 [ERROR] 直连测试失败: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/time (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x0000023A51697DD0>, 'Connection to fapi.binance.com timed out. (connect timeout=2)'))
2025-09-11 07:06:31 [INFO] 直连测试失败，启用代理
2025-09-11 07:06:31 [INFO] 自动代理设置完成，代理状态: True
2025-09-11 07:06:32 [INFO] Day-Breakout Strategy Starting...
2025-09-11 07:06:32 [INFO] 账户余额: 146.93 USDT
2025-09-11 07:06:32 [INFO] 交易时段: Asia Range, London Break, NY Momentum
2025-09-11 07:06:32 [INFO]   - Asia Range: 00:00-04:00 (杠杆3x)
2025-09-11 07:06:32 [INFO]   - London Break: 08:00-13:30 (杠杆5x)
2025-09-11 07:06:32 [INFO]   - NY Momentum: 13:30-22:00 (杠杆7x)
2025-09-11 07:06:32 [INFO] 周末过滤: 已启用
2025-09-11 07:06:32 [INFO] 复利模式: 已启用 (每日提取80%, 最大开仓20%)
2025-09-11 07:06:32 [INFO] 当前杠杆: 5x
2025-09-11 07:06:32 [INFO] [OK] 复利已禁用
