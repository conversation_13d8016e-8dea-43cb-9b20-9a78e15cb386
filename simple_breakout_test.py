#!/usr/bin/env python3
"""
简单测试突破策略逻辑的脚本
"""

def test_breakout_logic():
    # 模拟一些K线数据来测试逻辑
    # 格式: [时间, 开盘, 最高, 最低, 收盘]
    klines = [
        [1, 50000, 51000, 49000, 50500],  # 第1根K线
        [2, 50500, 51500, 50000, 51000],  # 第2根K线
        [3, 51000, 52000, 50500, 51800],  # 第3根K线
        [4, 51800, 52500, 51500, 52300],  # 第4根K线
        [5, 52300, 52400, 52000, 52350],  # 第5根K线（最新）
    ]
    
    print("模拟K线数据:")
    for i, k in enumerate(klines):
        print(f"  K线 {i+1}: 时间={k[0]}, 开盘={k[1]}, 最高={k[2]}, 最低={k[3]}, 收盘={k[4]}")
    
    # 计算动态指标
    highs = [float(k[2]) for k in klines]
    lows = [float(k[3]) for k in klines]
    high = max(highs[:-1])  # 不包含当前K线
    low = min(lows[:-1])
    curr_price = float(klines[-1][4])  # 当前收盘价
    
    print(f"\n计算结果:")
    print(f"历史最高价(不含最新K线): {high:.4f}")
    print(f"历史最低价(不含最新K线): {low:.4f}")
    print(f"当前价格(最新K线收盘价): {curr_price:.4f}")
    
    # 检查突破条件
    if curr_price > high:
        print(f"\n★★★ 突破新高! 当前价格 {curr_price:.4f} > 历史最高 {high:.4f}")
    elif curr_price < low:
        print(f"\n★★★ 跌破新低! 当前价格 {curr_price:.4f} < 历史最低 {low:.4f}")
    else:
        print(f"\n--- 未突破阈值")
        return False
    
    return True

if __name__ == "__main__":
    print("=== 突破策略逻辑测试 ===")
    result = test_breakout_logic()
    print(f"\n测试结果: {'通过' if result else '未通过'}")