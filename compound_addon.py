"""
300U傻瓜复利系统 - 全自动滚雪球
10分钟零依赖部署，复制即用
精英视角：让300U也能享受机构级资金管理
"""

import json
import logging
import time
from typing import Dict, Any, Optional
from decimal import Decimal


class CompoundAddonMixin:
    """傻瓜复利混入类"""
    
    def __init__(self):
        self.initial_nav = 300.0  # 初始净值
        self.peak_nav = 300.0     # 历史最高净值
        self.bullet_pool = 0.0    # 子弹池（利润暂存）
        self.spot_balance = 150.0 # 现货池
        self.futures_balance = 150.0 # 合约池
        self.logger = logging.getLogger(__name__)
    
    def _init_compound_account(self) -> bool:
        """
        账户初始化：一次性30秒完成300U拆分
        150U现货→活期理财，150U合约→交易池
        """
        if not self._is_compound_enabled():
            return False
        
        config = self._get_compound_config()
        total = config['total_capital']
        spot_target = total * config['spot_ratio']
        futures_target = total * config['futures_ratio']
        
        try:
            # 1. 现货→活期理财（2.8%年化，T+0）
            self.trader.transfer_spot('USDT', spot_target)
            self.trader.savings_purchase('USDT', spot_target)
            self.spot_balance = spot_target
            
            # 2. 合约资金池
            self.trader.transfer_futures('USDT', futures_target)
            self.futures_balance = futures_target
            
            self.initial_nav = total
            self.peak_nav = total
            
            self.logger.info(f"[复利初始化] 300U拆分完成")
            self.logger.info(f"[现货池] {spot_target}U → 活期2.8%年化")
            self.logger.info(f"[合约池] {futures_target}U → 交易资金")
            
            return True
            
        except Exception as e:
            self.logger.error(f"[初始化失败] {e}")
            return False
    
    def _auto_profit_withdraw(self, current_nav: float) -> bool:
        """
        HH30出金：净值上涨30%时，50%利润转现货
        实现利润锁定+复利滚动
        """
        if not self._is_compound_enabled():
            return False
        
        config = self._get_compound_config()
        threshold = config['profit_threshold']
        withdraw_ratio = config['profit_withdraw_ratio']
        
        if current_nav > self.peak_nav * threshold:
            profit = (current_nav - self.peak_nav) * withdraw_ratio
            
            try:
                # 从合约出金
                self.trader.transfer_spot('USDT', profit)
                
                # 立即投入活期理财
                self.trader.savings_purchase('USDT', profit)
                
                # 更新子弹池和峰值
                self.bullet_pool += profit
                self.peak_nav = current_nav
                
                self.logger.info(f"[HH30出金] {profit:.2f}U → 现货活期")
                self.logger.info(f"[子弹池] {self.bullet_pool:.2f}U")
                
                return True
                
            except Exception as e:
                self.logger.error(f"[出金失败] {e}")
        
        return False
    
    def _auto_bullet_return(self, current_drawdown: float) -> bool:
        """
        回撤冲回：回撤≤10%时，子弹池全部回合约
        实现逢低加仓，低买高卖
        """
        if not self._is_compound_enabled() or self.bullet_pool <= 0:
            return False
        
        config = self._get_compound_config()
        drawdown_threshold = config['drawdown_threshold']
        
        if current_drawdown <= drawdown_threshold:
            return_amount = self.bullet_pool
            
            try:
                # 从活期理财赎回
                self.trader.savings_redeem('USDT', return_amount)
                
                # 转回合约账户
                self.trader.transfer_futures('USDT', return_amount)
                
                self.logger.info(f"[回撤冲回] {return_amount:.2f}U → 合约")
                self.bullet_pool = 0
                
                return True
                
            except Exception as e:
                self.logger.error(f"[冲回失败] {e}")
        
        return False
    
    def _first_order_logic(self, symbol: str) -> bool:
        """
        首单逻辑：150U本金，≤25U，2×杠杆，3%硬止损
        严格执行资金管理，绝不梭哈
        """
        if not self._is_compound_enabled():
            return False
        
        # 检查是否已有持仓
        if self.trader.get_position(symbol) is not None:
            return False
        
        config = self._get_compound_config()
        
        try:
            # 获取合约可用余额
            available_balance = self.trader.get_futures_balance()
            
            # 预留5U手续费
            if available_balance <= 5:
                self.logger.warning("[首单跳过] 合约余额不足")
                return False
            
            # 计算首单大小：≤25U或17%本金
            max_order = min(available_balance * 0.17, config['first_order_max'])
            
            if max_order < 1:  # 最小交易单位
                return False
            
            # 设置杠杆
            self.trader.set_leverage(symbol, config['leverage'])
            
            # 市价开多
            order = self.trader.place_order(
                symbol=symbol,
                side='BUY',
                order_type='MARKET',
                quantity=max_order,
                reduce_only=False
            )
            
            if order and 'orderId' in order:
                # 获取入场价格
                entry_price = self.trader.get_current_price(symbol)
                
                # 设置3%硬止损
                sl_price = entry_price * (1 - config['stop_loss_pct']/100)
                
                self.trader.place_order(
                    symbol=symbol,
                    side='SELL',
                    order_type='STOP_MARKET',
                    quantity=max_order,
                    stop_price=sl_price,
                    reduce_only=True
                )
                
                self.logger.info(f"[首单开仓] {symbol} {max_order:.2f}U @ {entry_price:.4f}")
                self.logger.info(f"[止损设置] {sl_price:.4f} ({config['stop_loss_pct']}%)")
                
                return True
                
        except Exception as e:
            self.logger.error(f"[首单失败] {e}")
        
        return False
    
    def _calculate_drawdown(self, current_nav: float) -> float:
        """计算当前回撤"""
        if self.peak_nav <= 0:
            return 0.0
        return (self.peak_nav - current_nav) / self.peak_nav
    
    def _get_current_nav(self) -> float:
        """获取当前净值"""
        try:
            # 合约净值 + 现货活期 + 子弹池
            futures_nav = self.trader.get_total_balance()
            spot_value = self.trader.get_savings_balance()
            return futures_nav + spot_value + self.bullet_pool
        except:
            return self.initial_nav
    
    def _is_compound_enabled(self) -> bool:
        """检查复利功能是否启用"""
        return hasattr(self, 'cfg') and self.cfg.get('compound_addon', {}).get('enabled', False)
    
    def _get_compound_config(self) -> Dict[str, Any]:
        """获取复利配置"""
        return self.cfg.get('compound_addon', {})
    
    def _compound_auto_loop(self, symbol: str) -> None:
        """
        全自动循环：每轮1分钟
        300U傻瓜复利的核心引擎
        """
        if not self._is_compound_enabled():
            return
        
        try:
            # 1. 获取当前净值
            current_nav = self._get_current_nav()
            current_drawdown = self._calculate_drawdown(current_nav)
            
            # 2. HH30出金检查
            self._auto_profit_withdraw(current_nav)
            
            # 3. 回撤冲回检查
            self._auto_bullet_return(current_drawdown)
            
            # 4. 首单逻辑检查
            self._first_order_logic(symbol)
            
            # 5. 余额不足保护
            config = self._get_compound_config()
            if current_nav < config['min_balance_stop']:
                self.logger.warning("[余额保护] 净值低于300U，停止交易")
                return
                
            # 6. 每日日志
            self.logger.info(f"[净值] {current_nav:.2f}U | [回撤] {current_drawdown:.1%}")
            
        except Exception as e:
            self.logger.error(f"[复利循环失败] {e}")


class CompoundStrategy(CompoundAddonMixin):
    """完整的300U傻瓜复利策略"""
    
    def __init__(self, config_path: str = "compound_config.json"):
        super().__init__()
        
        # 加载配置
        try:
            with open(config_path, 'r') as f:
                self.cfg = json.load(f)
        except FileNotFoundError:
            self.cfg = {"compound_addon": {"enabled": False}}
            self.logger.warning("复利配置未找到，已禁用")
    
    def process_symbol(self, symbol: str) -> None:
        """主处理函数，集成傻瓜复利"""
        # 初始化账户（一次性）
        if self.initial_nav == 300.0:  # 默认值表示未初始化
            self._init_compound_account()
        
        # 运行全自动复利循环
        self._compound_auto_loop(symbol)


# 快速测试函数
def test_compound_system():
    """测试300U傻瓜复利系统"""
    logging.basicConfig(level=logging.INFO)
    
    strategy = CompoundStrategy()
    
    # 测试配置
    print("300U傻瓜复利配置:")
    print(json.dumps(strategy.cfg, indent=2))
    
    # 测试参数计算
    config = strategy._get_compound_config()
    total = config['total_capital']
    spot = total * config['spot_ratio']
    futures = total * config['futures_ratio']
    first_order = min(futures * 0.17, config['first_order_max'])
    
    print(f"\n资金分配:")
    print(f"总资金: {total}U")
    print(f"现货池: {spot}U (活期2.8%)")
    print(f"合约池: {futures}U")
    print(f"首单限制: {first_order}U (2×杠杆)")
    
    print("\n✅ 300U傻瓜复利系统已就绪！10分钟落地完成！")


if __name__ == "__main__":
    test_compound_system()