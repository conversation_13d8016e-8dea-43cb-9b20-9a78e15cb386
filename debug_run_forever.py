#!/usr/bin/env python3
"""
调试run_forever方法的脚本
"""

import json
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_run_forever():
    try:
        # 导入必要的模块
        from day_breakout_strategy import DayBreakoutStrategy
        from binance_trader import BinanceTrader
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        
        # 创建策略实例
        strategy = DayBreakoutStrategy(trader, config)
        
        # 获取所有永续合约
        futures = trader.get_all_futures()
        print(f"总共获取到 {len(futures)} 个交易对")
        
        # 只测试前10个交易对以节省时间
        test_symbols = futures[:10]
        print(f"测试交易对: {test_symbols}")
        
        # 统计符合条件的币数
        valid_coins_count = 0
        processed_coins_count = 0
        
        # 测试处理单个交易对的逻辑
        for symbol in test_symbols:
            print(f"\n测试处理 {symbol}:")
            try:
                # 调用process_symbol方法
                result = strategy.process_symbol(symbol)
                processed_coins_count += 1
                
                if result:
                    valid_coins_count += 1
                    print(f"  {symbol} 处理完成并符合条件")
                else:
                    print(f"  {symbol} 处理完成但不符合条件")
                    
            except Exception as e:
                print(f"  {symbol} 处理出错: {e}")
                import traceback
                traceback.print_exc()
        
        print(f"\n统计结果:")
        print(f"  处理的币数: {processed_coins_count}")
        print(f"  符合条件的币数: {valid_coins_count}")
        print(f"  不符合条件的币数: {processed_coins_count - valid_coins_count}")
                
    except Exception as e:
        print(f"测试策略时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_run_forever()