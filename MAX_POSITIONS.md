# 最大持仓数量限制说明

## 概述

本策略现在支持最大持仓数量限制功能，以防止同时持有很多币种。默认情况下，策略没有持仓数量限制，但可以通过配置文件轻松设置。

## 配置选项

### 1. 无限制持仓（默认）

当配置文件中不包含[max_positions](file://d:\roll\day_breakout\day_breakout_final\config.json#L5-L5)键或设置为`null`时，策略不会限制持仓数量：

```json
// 不包含max_positions键或设置为null
"max_positions": null
// 或者直接不写这个键
```

### 2. 限制持仓数量

通过在配置文件中设置[max_positions](file://d:\roll\day_breakout\day_breakout_final\config.json#L5-L5)键来限制最大持仓数量：

```json
"max_positions": 5   // 最多同时持有5个币种
```

## 实现细节

在[day_breakout_strategy.py](file://d:\roll\day_breakout\day_breakout_final\day_breakout_strategy.py)的[open_position()](file://d:\roll\day_breakout\day_breakout_final\day_breakout_strategy.py#L153-L213)方法中实现了持仓数量检查：

```python
# ① 已持仓计数
current_positions = len(self.trader.get_positions())

# ② 硬锁上限
max_pos = self.cfg.get('max_positions', None)   # 默认无上限
if max_pos is not None and current_positions >= max_pos:
    self.logger.warning(f"已达最大持仓数 {max_pos}，跳过 {symbol}")
    return
```

## 使用建议

1. **保守型投资者**：建议设置较低的最大持仓数（如3-5个）
2. **激进型投资者**：可以设置较高的最大持仓数（如10-15个）
3. **无限制模式**：适合资金充足且风险承受能力强的投资者

## 验证脚本

使用以下脚本检查当前持仓情况：

```bash
python check_positions.py
```

输出示例：
```
当前持仓数量: 0
最大持仓限制: 5

当前无持仓
```

## 配置示例

### 设置最大持仓数为5个
```json
"max_positions": 5
```

### 设置最大持仓数为10个
```json
"max_positions": 10
```

### 移除持仓限制（无上限）
```json
"max_positions": null
// 或者直接删除这个键
```

## 注意事项

1. 最大持仓数限制仅在开仓时检查，不影响已有的持仓
2. 当达到最大持仓数时，策略会跳过新的开仓机会并记录警告日志
3. 建议根据账户资金和风险承受能力合理设置最大持仓数