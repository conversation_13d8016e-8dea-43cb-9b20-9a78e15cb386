#!/usr/bin/env python3
"""
时间窗口回测脚本
用于比较不同交易时段窗口的策略表现
"""

import sys
import os
import json
import argparse
from datetime import datetime, timedelta
import pandas as pd

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def load_klines_data(symbol, period):
    """
    加载K线数据（模拟）
    实际实现中应从交易所API或本地数据文件加载
    """
    # 这里是模拟数据，实际应用中应替换为真实数据加载逻辑
    print(f"加载 {symbol} 在 {period} 期间的K线数据...")
    # 模拟返回一些K线数据
    return [
        # [timestamp, open, high, low, close, volume]
        [1704067200000, '100', '105', '95', '102', '1000'],
        [1704067500000, '102', '108', '101', '105', '1200'],
        # ... 更多数据
    ]

def calculate_pnl(klines, window_start, window_end):
    """
    计算特定时间窗口内的PNL
    """
    # 这里是简化的PNL计算逻辑，实际应用中应根据策略逻辑实现
    total_pnl = 0
    trades = 0
    
    for kline in klines:
        timestamp = kline[0]
        dt = datetime.fromtimestamp(timestamp/1000)
        
        # 检查是否在交易窗口内
        if window_start <= dt.hour < window_end:
            # 简化的PNL计算
            open_price = float(kline[1])
            close_price = float(kline[4])
            pnl = (close_price - open_price) / open_price * 100
            total_pnl += pnl
            trades += 1
    
    return total_pnl, trades

def backtest_window(symbol, klines, window_name, window_config):
    """
    回测特定时间窗口
    """
    print(f"回测 {symbol} 的 {window_name} 窗口...")
    
    if window_name == "full":
        # 全时段回测
        pnl, trades = calculate_pnl(klines, 0, 24)
    else:
        # 特定时段回测
        start_hour = window_config['start_hour']
        end_hour = window_config['end_hour']
        pnl, trades = calculate_pnl(klines, start_hour, end_hour)
    
    # 简化的夏普比率和最大回撤计算
    sharpe = pnl / 10 if trades > 0 else 0  # 简化计算
    max_dd = -abs(pnl * 0.2)  # 简化计算
    
    return {
        "窗口": window_name,
        "触发": trades,
        "AvgPNL": f"{pnl:.1f} %" if trades > 0 else "0.0 %",
        "Sharpe": f"{sharpe:.1f}",
        "MaxDD": f"{max_dd:.1f} %"
    }

def main():
    parser = argparse.ArgumentParser(description='时间窗口回测脚本')
    parser.add_argument('--strategy', default='day_breakout', help='策略名称')
    parser.add_argument('--symbols', required=True, help='交易对列表，用逗号分隔')
    parser.add_argument('--windows', default='09:30-16:00,21:30-04:00,full', help='时间窗口列表')
    parser.add_argument('--period', required=True, help='回测周期，格式：YYYY-MM-DD→YYYY-MM-DD')
    parser.add_argument('--output', default='console', help='输出格式：console/csv')
    
    args = parser.parse_args()
    
    # 解析参数
    symbols = args.symbols.split(',')
    windows = args.windows.split(',')
    period = args.period
    
    # 定义时间窗口配置
    window_configs = {
        "09:30-16:00": {"start_hour": 1, "end_hour": 8},  # UTC时间
        "21:30-04:00": {"start_hour": 13, "end_hour": 20},  # UTC时间
        "full": {}
    }
    
    # 存储结果
    results = []
    
    print(f"开始回测 {args.strategy} 策略...")
    print(f"交易对: {', '.join(symbols)}")
    print(f"时间窗口: {', '.join(windows)}")
    print(f"回测周期: {period}")
    
    # 对每个交易对进行回测
    for symbol in symbols:
        print(f"\n--- 回测 {symbol} ---")
        # 加载数据
        klines = load_klines_data(symbol, period)
        
        # 对每个时间窗口进行回测
        for window in windows:
            if window in window_configs:
                result = backtest_window(symbol, klines, window, window_configs[window])
                result["交易对"] = symbol
                results.append(result)
            else:
                print(f"警告: 未知的时间窗口 {window}")
    
    # 输出结果
    if args.output == 'csv':
        # 输出为CSV格式
        df = pd.DataFrame(results)
        print("\nCSV格式结果:")
        print(df.to_csv(index=False))
    else:
        # 输出到控制台
        print("\n回测结果:")
        df = pd.DataFrame(results)
        print(df.to_string(index=False))

if __name__ == "__main__":
    main()