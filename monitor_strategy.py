#!/usr/bin/env python3
"""
策略监控脚本
用于实时监控策略运行状态和日志输出
"""

import time
import os
from datetime import datetime

def monitor_log():
    """监控日志文件"""
    log_file = "day_breakout.log"
    
    # 如果日志文件不存在，创建一个空文件
    if not os.path.exists(log_file):
        open(log_file, 'w').close()
    
    # 获取文件的初始大小
    last_position = 0
    
    print("开始监控策略日志...")
    print("按 Ctrl+C 停止监控")
    print("-" * 50)
    
    try:
        while True:
            # 检查文件是否被截断（日志轮转）
            current_size = os.path.getsize(log_file)
            if current_size < last_position:
                last_position = 0  # 重置到文件开始位置
            
            # 打开文件并移动到上次读取的位置
            with open(log_file, 'r', encoding='utf-8') as f:
                f.seek(last_position)
                new_content = f.read()
                last_position = f.tell()
            
            # 如果有新内容，打印出来
            if new_content:
                print(new_content, end='')
            
            # 每秒检查一次
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n停止监控")

def check_positions():
    """检查当前持仓情况"""
    try:
        import json
        from binance_trader import BinanceTrader
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        
        # 获取当前持仓
        positions = trader.get_positions()
        
        print(f"\n当前时间: {datetime.now()}")
        print(f"当前持仓数量: {len(positions)}")
        
        # 显示最大持仓配置
        max_positions = config.get('max_positions', None)
        print(f"最大持仓限制: {max_positions if max_positions is not None else '无限制'}")
        
        if positions:
            print("\n当前持仓详情:")
            for symbol, pos in positions.items():
                qty = float(pos['positionAmt'])
                entry_price = float(pos['entryPrice'])
                unrealized_pnl = float(pos['unRealizedProfit'] or 0)
                print(f"  {symbol}: {qty} (入场价: {entry_price}, 未实现盈亏: {unrealized_pnl:.2f})")
        else:
            print("\n当前无持仓")
            
    except Exception as e:
        print(f"检查持仓时出错: {e}")

def check_config():
    """检查当前配置"""
    try:
        import json
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"\n当前配置:")
        print(f"  窗口类型: {config.get('window_type', 'day')}")
        print(f"  最大持仓数: {config.get('max_positions', 5)}")
        print(f"  杠杆设置: {config.get('leverage', {}).get('target_leverage', 10)}x")
        print(f"  复利功能: {'启用' if config.get('compounding', {}).get('enabled', False) else '禁用'}")
        print(f"  止损比例: {config.get('stop_loss_pct', 3)}%")
        
    except Exception as e:
        print(f"检查配置时出错: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "log":
            monitor_log()
        elif sys.argv[1] == "positions":
            check_positions()
        elif sys.argv[1] == "config":
            check_config()
        else:
            print("用法: python monitor_strategy.py [log|positions|config]")
    else:
        print("策略监控工具")
        print("可用命令:")
        print("  python monitor_strategy.py log       - 监控日志")
        print("  python monitor_strategy.py positions - 检查持仓")
        print("  python monitor_strategy.py config    - 检查配置")