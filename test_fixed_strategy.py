#!/usr/bin/env python3
"""
测试修复后的策略
"""

import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_fixed_strategy():
    try:
        print("=== 测试修复后的策略 ===")
        
        # 导入必要的模块
        from day_breakout_strategy import DayBreakoutStrategy
        from binance_trader import BinanceTrader
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        
        # 创建策略实例
        strategy = DayBreakoutStrategy(trader, config)
        
        # 获取所有交易对
        all_futures = trader.get_all_futures()
        print(f"总共获取到 {len(all_futures)} 个交易对")
        
        # 测试前5个交易对
        test_symbols = all_futures[:5]
        
        for symbol in test_symbols:
            print(f"\n--- 测试 {symbol} ---")
            
            # 执行策略处理
            try:
                strategy.process_symbol(symbol)
                print(f"✓ {symbol} 处理完成")
            except Exception as e:
                print(f"✗ {symbol} 处理出错: {e}")
        
        print("\n=== 测试完成 ===")
        print("如果策略逻辑正确，应该能看到相应的日志输出")
        print("包括：")
        print("1. 符合条件的币会显示开仓信息")
        print("2. 开仓成功的会显示订单ID")
        print("3. 开仓后会设置止损订单")
                
    except Exception as e:
        print(f"测试修复后的策略时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fixed_strategy()