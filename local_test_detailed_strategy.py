#!/usr/bin/env python3
"""
本地测试详细策略逻辑 - 不连接实际API
"""

import json
import sys
import os
import logging
from unittest.mock import Mock, MagicMock
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')

def test_detailed_strategy_locally():
    try:
        print("=== 本地测试详细策略逻辑 ===")
        
        # 导入策略类
        from detailed_day_breakout_strategy import DayBreakoutStrategy
        
        # 创建模拟的交易器
        mock_trader = Mock()
        
        # 模拟交易器的方法返回值
        mock_trader.get_all_futures.return_value = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
        mock_trader.get_klines.return_value = [
            [None, '100', '105', '95', '102', '1000000'],  # 开, 高, 低, 收, 成交量
            [None, '102', '108', '101', '107', '1500000']
        ]
        mock_trader.get_symbol_ticker.return_value = {'price': '107.5'}
        mock_trader.get_ticker.return_value = {
            'priceChangePercent': '12.5',
            'quoteVolume': '1500000'
        }
        mock_trader.get_position.return_value = {'positionAmt': '0', 'entryPrice': '0'}
        mock_trader.get_total_balance.return_value = 1000.0
        mock_trader.get_all_positions.return_value = []
        mock_trader.set_leverage.return_value = True
        mock_trader.place_order.return_value = {'orderId': '123456789'}
        
        # 模拟HTTP请求
        mock_trader.http = Mock()
        mock_trader.http.get.return_value = {
            'symbols': [
                {
                    'symbol': 'BTCUSDT',
                    'listingTime': (datetime.utcnow() - timedelta(days=30)).timestamp() * 1000
                }
            ]
        }
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建策略实例
        strategy = DayBreakoutStrategy(mock_trader, config)
        
        # 选择一个交易对进行测试
        symbol = 'BTCUSDT'
        print(f"\n=== 测试交易对: {symbol} ===")
        
        # 执行策略处理逻辑
        print("执行策略处理...")
        result = strategy.process_symbol(symbol)
        print(f"策略处理结果: {'成功' if result else '失败'}")
        
        # 验证调用
        print("\n=== 调用验证 ===")
        print(f"get_klines 调用次数: {mock_trader.get_klines.call_count}")
        print(f"get_symbol_ticker 调用次数: {mock_trader.get_symbol_ticker.call_count}")
        print(f"get_ticker 调用次数: {mock_trader.get_ticker.call_count}")
        print(f"get_position 调用次数: {mock_trader.get_position.call_count}")
        print(f"set_leverage 调用次数: {mock_trader.set_leverage.call_count}")
        print(f"place_order 调用次数: {mock_trader.place_order.call_count}")
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"本地测试详细策略时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_detailed_strategy_locally()