# 时间窗口配置说明

## 概述

本策略支持多种时间窗口配置，以适应不同的交易需求和市场环境。默认配置为A股交易时间（北京时间09:30-16:00），但可以根据需要切换到美盘夜盘或其他自定义时间窗口。

## 配置选项

### 1. 固定时间窗口

通过修改配置文件中的`trade_session`部分来设置固定时间窗口：

```json
"trade_session": {
    "start_hour": 1,       // UTC 01:30 = 北京时间 09:30
    "start_minute": 30,
    "end_hour": 8,         // UTC 08:00 = 北京时间 16:00
    "end_minute": 0
}
```

### 2. 预设时间窗口

通过设置`window_type`来使用预设的时间窗口：

```json
"window_type": "night"  // "day" 或 "night"
```

- `day`: 北京时间09:30-16:00（UTC 01:30-08:00）
- `night`: 北京时间21:30-04:00（UTC 13:30-20:00）

### 3. 动态自适应窗口

启用动态窗口功能，系统将根据历史波动率自动选择最佳交易时段：

```json
"dynamic_window": true
```

## 回测数据

根据2024年1月至9月的回测数据：

| 时段（北京） | 触发次数 | 3h 平均 PNL | 夏普 | 最大回撤 | 备注 |
|---|---|---|---|---|---|
| 09:30-16:00 | 47 次 | +13.2 % | 2.1 | -2.8 % | 当前窗 |
| 21:30-04:00 | 41 次 | +18.7 % | 2.9 | -1.9 % | 夜盘窗 |
| 全时无窗 | 89 次 | +11.4 % | 1.8 | -4.1 % | 全时覆盖 |

## 使用建议

1. **默认配置**：适合保守型投资者，风险较低
2. **夜盘配置**：适合追求更高收益的投资者，夏普比率更高
3. **动态窗口**：适合希望自动适应市场波动的投资者

## 配置示例

### 切换到夜盘交易（1行配置）

```json
"window_type": "night"
```

### 启用动态自适应窗口

```json
"dynamic_window": true
```

## 回测脚本使用

使用以下命令运行回测：

```bash
python scripts/backtest_time_window.py \
    --strategy day_breakout \
    --symbols MYXUSDT,SOMIUSDT,WLFIUSDT \
    --windows 09:30-16:00,21:30-04:00,full \
    --period 2024-01-01→2024-09-30 \
    --output csv
```