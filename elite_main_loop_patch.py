"""
主循环补丁：添加趋势模式开关
只需在process_symbol开头添加3行代码
"""

# 补丁内容：在原有的process_symbol方法开头添加以下代码

PATCH_CODE = '''
        # === 精英趋势补丁开始 ===
        self._reset_pyramid_count()  # 重置加仓计数器
        if self._should_enable_trend_mode(symbol):
            self.logger.info(f"{symbol} 日线突破，启用趋势模式")
        # === 精英趋势补丁结束 ===
'''

# 使用说明：
# 1. 找到原有的process_symbol方法
# 2. 在方法开头粘贴上述3行代码
# 3. 确保类继承了EliteTrendAddonMixin

# 完整的修改后示例：
EXAMPLE_MODIFIED_METHOD = '''
def process_symbol(self, symbol: str) -> None:
    """处理单个交易对"""
    # === 精英趋势补丁开始 ===
    self._reset_pyramid_count()  # 重置加仓计数器
    if self._should_enable_trend_mode(symbol):
        self.logger.info(f"{symbol} 日线突破，启用趋势模式")
    # === 精英趋势补丁结束 ===
    
    # 原有的5分钟入场逻辑继续执行...
    # 这里放原有的代码...
    
    # 在持仓管理部分，添加浮盈加仓检查
    if hasattr(self, 'position') and self.position > 0:
        self._pyramid_if_profitable(symbol, self.avg_entry_price)
'''

# 快速应用脚本
def apply_patch_to_file(original_file: str, output_file: str = None):
    """
    自动应用补丁到策略文件
    """
    import re
    
    if output_file is None:
        output_file = original_file.replace('.py', '_elite.py')
    
    with open(original_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找process_symbol方法
    pattern = r'(def\s+process_symbol\s*\(\s*self\s*,\s*symbol\s*:\s*str\s*\)\s*->\s*None\s*:\s*\n)(.*?)(?=\n\s*def|\n\s*class|\Z)'
    
    def replacer(match):
        method_def = match.group(1)
        method_body = match.group(2)
        
        # 添加补丁代码
        patched_body = '\n    # === 精英趋势补丁开始 ===\n    self._reset_pyramid_count()  # 重置加仓计数器\n    if self._should_enable_trend_mode(symbol):\n        self.logger.info(f"{symbol} 日线突破，启用趋势模式")\n    # === 精英趋势补丁结束 ===\n\n' + method_body
        
        return method_def + patched_body
    
    patched_content = re.sub(pattern, replacer, content, flags=re.DOTALL)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(patched_content)
    
    print(f"补丁已应用到: {output_file}")
    return output_file

if __name__ == "__main__":
    print("精英主循环补丁已创建！")
    print("\n使用方法:")
    print("1. 复制 PATCH_CODE 到 process_symbol 方法开头")
    print("2. 或运行 apply_patch_to_file('day_breakout_strategy.py') 自动应用")
    print("\n修改完成！30分钟落地达成！")