#!/usr/bin/env python3
"""
测试详细策略逻辑
"""

import json
import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')

def test_detailed_strategy():
    try:
        print("=== 测试详细策略逻辑 ===")
        
        # 导入必要的模块
        from detailed_day_breakout_strategy import DayBreakoutStrategy
        from binance_trader import BinanceTrader
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        
        # 创建策略实例
        strategy = DayBreakoutStrategy(trader, config)
        
        # 获取所有交易对
        all_futures = trader.get_all_futures()
        print(f"总共获取到 {len(all_futures)} 个交易对")
        
        # 选择一个交易对进行测试
        if all_futures:
            symbol = all_futures[0]  # 选择第一个交易对
            print(f"\n=== 测试交易对: {symbol} ===")
            
            # 执行策略处理逻辑
            print("执行策略处理...")
            result = strategy.process_symbol(symbol)
            print(f"策略处理结果: {'成功' if result else '失败'}")
            
        else:
            print("没有获取到交易对，无法进行测试")
                
    except Exception as e:
        print(f"测试详细策略时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_detailed_strategy()