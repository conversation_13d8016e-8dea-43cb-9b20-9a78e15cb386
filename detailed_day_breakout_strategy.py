#!/usr/bin/env python3
"""
详细的DayBreakout策略实现 - 详细记录每个筛选条件
"""

import json
import sys
import os
import time
import logging
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class DayBreakoutStrategy:
    def __init__(self, trader, config):
        self.trader = trader
        self.cfg = config
        self.logger = logging.getLogger(__name__)
        
        # 初始化每日统计
        self.decay_ref = datetime.utcnow()
        self.today_opened_usd = 0
        
    def reset_daily(self):
        """重置每日统计"""
        self.decay_ref = datetime.utcnow()
        self.today_opened_usd = 0
        self.logger.info("[OK] 北京00:00已重置")
        
    def _is_new_coin(self, symbol):
        """检查是否为新币（上市时间≤90天）"""
        try:
            # 获取交易对信息
            exchange_info = self.trader.http.get('/fapi/v1/exchangeInfo')
            if not exchange_info or 'symbols' not in exchange_info:
                return False
                
            for sym_info in exchange_info['symbols']:
                if sym_info['symbol'] == symbol:
                    # 获取上市时间
                    listing_time = sym_info.get('listingTime', 0)
                    if listing_time:
                        listing_date = datetime.fromtimestamp(listing_time / 1000)
                        days_since_listing = (datetime.utcnow() - listing_date).days
                        return days_since_listing <= 90
            return False
        except Exception as e:
            self.logger.error(f"检查新币时出错: {e}")
            return False
            
    def _has_open_leverage(self, symbol):
        """检查交易对是否已开放杠杆（只检查是否在有效交易对列表中）"""
        try:
            # 获取所有有效交易对
            futures = self.trader.get_all_futures()
            return symbol in futures
        except Exception as e:
            self.logger.error(f"检查杠杆时出错: {e}")
            return False
            
    def _calculate_atr_3x(self, symbol):
        """计算ATR 3倍值"""
        try:
            # 获取K线数据
            klines = self.trader.get_klines(symbol, interval='1d', limit=15)
            if not klines or len(klines) < 2:
                return 0
                
            # 计算ATR
            trs = []
            for i in range(1, len(klines)):
                high = float(klines[i][2])
                low = float(klines[i][3])
                prev_close = float(klines[i-1][4])
                tr = max(high - low, abs(high - prev_close), abs(low - prev_close))
                trs.append(tr)
                
            if not trs:
                return 0
                
            atr = sum(trs) / len(trs)
            current_price = float(klines[-1][4])
            atr_pct = (atr / current_price) * 100 if current_price > 0 else 0
            
            return atr_pct * 3  # 返回3倍ATR百分比
        except Exception as e:
            self.logger.error(f"计算ATR时出错: {e}")
            return 0
            
    def _place_stop_loss_order(self, symbol, quantity, entry_price):
        """设置止损订单"""
        try:
            # 计算止损价格（3%固定止损）
            stop_loss_pct = self.cfg.get('stop_loss_pct', 3)
            stop_price = entry_price * (1 - stop_loss_pct / 100)
            
            # 设置止损订单
            order = self.trader.place_order(
                symbol=symbol,
                side='SELL',
                order_type='STOP_MARKET',
                quantity=quantity,
                stop_price=stop_price,
                reduce_only=True
            )
            
            if order and 'orderId' in order:
                self.logger.info(f"[止损] {symbol} 设置止损订单成功 @{stop_price:.4f}, 订单ID: {order['orderId']}")
                return True
            else:
                self.logger.error(f"[止损] {symbol} 设置止损订单失败")
                return False
        except Exception as e:
            self.logger.error(f"设置止损订单时出错: {e}")
            return False

    def process_symbol(self, symbol):
        """处理单个交易对 - 详细记录每个筛选条件"""
        try:
            self.logger.info(f"=== 开始处理交易对 {symbol} ===")
            
            # 1. 检查是否为新币（上市时间≤90天）
            self.logger.info(f"[条件1] 检查是否为新币（上市时间≤90天）")
            is_new = self._is_new_coin(symbol)
            self.logger.info(f"[条件1] 结果: {'通过' if is_new else '未通过'} - {symbol} {'是' if is_new else '不是'}新币")
            
            # 2. 检查是否已开放杠杆
            self.logger.info(f"[条件2] 检查是否已开放杠杆")
            has_leverage = self._has_open_leverage(symbol)
            self.logger.info(f"[条件2] 结果: {'通过' if has_leverage else '未通过'} - {symbol} 杠杆{'已开放' if has_leverage else '未开放'}")
            
            # 如果不是新币或未开放杠杆，跳过
            if not (is_new and has_leverage):
                self.logger.info(f"[总体结果] {symbol} 未通过基础条件检查，跳过处理")
                return False
                
            # 3. 检查日期是否过期
            self.logger.info(f"[条件3] 检查日期是否过期")
            now = datetime.utcnow()
            if now > self.decay_ref + timedelta(days=1):
                self.reset_daily()
                self.logger.info(f"[条件3] 结果: 日期已重置")
            else:
                self.logger.info(f"[条件3] 结果: 日期未过期，继续处理")
                
            # 4. 获取当前交易时段
            self.logger.info(f"[条件4] 检查交易时段")
            trade = self.cfg['trade_session']
            curr_hour = (now.hour + 8) % 24  # 转为北京时间
            curr_minute = now.minute
            
            # 转换为分钟来比较
            curr_time = curr_hour * 60 + curr_minute
            start_hour = trade['start_hour'] 
            start_minute = trade['start_minute']
            end_hour = trade['end_hour']
            end_minute = trade['end_minute']
            start_time = start_hour * 60 + start_minute
            end_time = end_hour * 60 + end_minute
            
            # 跨夜处理
            in_trading_session = False
            if end_time < start_time:  # 跨夜交易时段
                if curr_time < end_time or curr_time >= start_time:
                    in_trading_session = True
            else:  # 普通交易时段
                if start_time <= curr_time < end_time:
                    in_trading_session = True
                    
            self.logger.info(f"[条件4] 结果: {'通过' if in_trading_session else '未通过'} - "
                           f"当前时间 {curr_hour:02d}:{curr_minute:02d} "
                           f"(交易时段 {start_hour:02d}:{start_minute:02d}-{end_hour:02d}:{end_minute:02d})")
            
            if not in_trading_session:
                self.logger.info(f"[总体结果] {symbol} 不在交易时段内，跳过处理")
                return False
                
            # 5. 获取K线数据
            self.logger.info(f"[条件5] 获取K线数据")
            klines = self.trader.get_klines(symbol)
            if not klines:
                self.logger.info(f"[条件5] 结果: 未通过 - 无法获取K线数据")
                self.logger.info(f"[总体结果] {symbol} 无法获取K线数据，跳过处理")
                return False
            self.logger.info(f"[条件5] 结果: 通过 - 获取到 {len(klines)} 根K线")
            
            # 6. 计算动态指标
            self.logger.info(f"[条件6] 计算动态指标")
            highs = [float(k[2]) for k in klines]
            lows = [float(k[3]) for k in klines]
            high = max(highs[:-1])  # 不包含当前K线
            low = min(lows[:-1])
            self.logger.info(f"[条件6] 结果: 通过 - 前高 {high:.4f}, 前低 {low:.4f}")
            
            # 7. 5m筛选：检查最近的5m K线涨幅和成交量
            self.logger.info(f"[条件7] 5分钟K线筛选（涨幅≥3%且成交量≥1.5×均量）")
            vol_ratio = 0
            five_min_valid = False
            if len(klines) >= 2:
                # 获取最近两根K线的数据
                recent_kline = klines[-1]  # 最近的K线
                prev_kline = klines[-2]    # 前一根K线
                
                # 计算5m涨幅
                recent_open = float(recent_kline[1])
                recent_close = float(recent_kline[4])
                recent_change_pct = ((recent_close - recent_open) / recent_open) * 100 if recent_open != 0 else 0
                
                # 计算成交量比率
                recent_vol = float(recent_kline[5])
                prev_vol = float(prev_kline[5])
                vol_ratio = recent_vol / prev_vol if prev_vol > 0 else 0
                
                # 5m筛选：涨幅≥3%且成交量≥1.5×均量
                five_min_valid = recent_change_pct >= 3 and vol_ratio >= 1.5
                
                self.logger.info(f"[条件7] 结果: {'通过' if five_min_valid else '未通过'} - "
                               f"涨幅 {recent_change_pct:.2f}% ({'≥3%' if recent_change_pct >= 3 else '<3%'}), "
                               f"成交量比率 {vol_ratio:.2f}x ({'≥1.5x' if vol_ratio >= 1.5 else '<1.5x'})")
            else:
                self.logger.info(f"[条件7] 结果: 未通过 - K线数据不足")
                
            if not five_min_valid:
                self.logger.info(f"[总体结果] {symbol} 未通过5分钟筛选，跳过处理")
                return False
                
            # 8. 获取当前价格
            self.logger.info(f"[条件8] 获取当前价格")
            curr_price = float(klines[-1][4])  # 当前收盘价
            self.logger.info(f"[条件8] 结果: 通过 - 当前价格 {curr_price:.4f}")
            
            # 9. 检查价格突破（提前检查，避免不必要的API调用）
            self.logger.info(f"[条件9] 检查持仓情况")
            curr_pos = self.trader.get_position(symbol)
            pos_amt = float(curr_pos['positionAmt']) if curr_pos else 0
            self.logger.info(f"[条件9] 结果: 通过 - 当前持仓 {pos_amt}")
            
            # 如果已经有持仓，则进行持仓管理
            if pos_amt != 0:
                self.logger.info(f"[持仓管理] {symbol} 已有持仓 {pos_amt}，进行持仓管理")
                
                # 获取当前价格
                ticker = self.trader.get_symbol_ticker(symbol)
                if not ticker:
                    self.logger.error(f"获取{symbol}价格失败，跳过")
                    return False
                current_price = float(ticker['price'])
                self.logger.info(f"[持仓管理] 当前价格 {current_price:.4f}")
                
                # 获取持仓均价
                entry_price = float(curr_pos['entryPrice'])
                self.logger.info(f"[持仓管理] 持仓均价 {entry_price:.4f}")
                
                # 计算浮动盈亏百分比
                pnl_pct = (current_price - entry_price) / entry_price * 100
                if pos_amt < 0:  # 空仓
                    pnl_pct = -pnl_pct
                self.logger.info(f"[持仓管理] 浮动盈亏 {pnl_pct:.2f}%")
                
                # 止盈止损
                stop_loss_pct = -self.cfg['stop_loss_pct']
                take_profit_pct = self.cfg['take_profit_pct']
                
                if pnl_pct <= stop_loss_pct:  # 止损
                    self.logger.info(f"[持仓管理] 触发止损条件 (盈亏 {pnl_pct:.2f}% <= {stop_loss_pct}%)")
                    side = 'BUY' if pos_amt < 0 else 'SELL'
                    order = self.trader.place_order(
                        symbol=symbol,
                        side=side,
                        order_type='MARKET',
                        quantity=abs(pos_amt),
                        reduce_only=True
                    )
                    if order and 'orderId' in order:
                        self.logger.info(f"[止损] {symbol} 平仓 @{current_price:.4f}, 订单ID: {order['orderId']}")
                    elif order:
                        self.logger.error(f"[止损] {symbol} 平仓失败，订单响应异常: {order}")
                    else:
                        self.logger.error(f"[止损] {symbol} 平仓失败")
                        
                elif pnl_pct >= take_profit_pct:  # 止盈
                    self.logger.info(f"[持仓管理] 触发止盈条件 (盈亏 {pnl_pct:.2f}% >= {take_profit_pct}%)")
                    side = 'BUY' if pos_amt < 0 else 'SELL'
                    order = self.trader.place_order(
                        symbol=symbol,
                        side=side,
                        order_type='MARKET',
                        quantity=abs(pos_amt),
                        reduce_only=True
                    )
                    if order and 'orderId' in order:
                        self.logger.info(f"[止盈] {symbol} 平仓 @{current_price:.4f}, 订单ID: {order['orderId']}")
                    elif order:
                        self.logger.error(f"[止盈] {symbol} 平仓失败，订单响应异常: {order}")
                    else:
                        self.logger.error(f"[止盈] {symbol} 平仓失败")
                else:
                    self.logger.info(f"[持仓管理] 未触发止盈止损条件，继续持有")
                
                # 如果已经有持仓，不需要再检查开仓条件
                self.logger.info(f"[总体结果] {symbol} 已处理持仓管理")
                return True  # 已处理
            
            # 如果没有持仓，检查开仓条件
            self.logger.info(f"[开仓检查] {symbol} 无持仓，检查开仓条件")
            
            # 10. 再次获取当前价格用于开仓判断
            self.logger.info(f"[条件10] 再次获取当前价格用于开仓判断")
            ticker = self.trader.get_symbol_ticker(symbol)
            if not ticker:
                self.logger.error(f"获取{symbol}价格失败，跳过")
                self.logger.info(f"[总体结果] {symbol} 无法获取当前价格，跳过处理")
                return False
            current_price = float(ticker['price'])
            self.logger.info(f"[条件10] 结果: 通过 - 当前价格 {current_price:.4f}")
            
            # 11. 获取24小时统计信息
            self.logger.info(f"[条件11] 获取24小时统计信息")
            ticker_24h = self.trader.get_ticker(symbol)
            if not ticker_24h:
                self.logger.error(f"获取{symbol}24小时统计信息失败，跳过")
                self.logger.info(f"[总体结果] {symbol} 无法获取24小时统计信息，跳过处理")
                return False
            self.logger.info(f"[条件11] 结果: 通过 - 成功获取24小时统计信息")
            
            # 12. 获取24小时涨幅和成交量
            self.logger.info(f"[条件12] 获取24小时涨幅和成交量")
            price_change_percent = float(ticker_24h.get('priceChangePercent', 0))
            quote_volume = float(ticker_24h.get('quoteVolume', 0))
            self.logger.info(f"[条件12] 结果: 通过 - 24小时涨幅 {price_change_percent:.2f}%, 24小时成交量 {quote_volume:.0f} USDT")
            
            # 13. 价格≤100 USDT + 24h涨幅≥8% + 量≥100万 USD过滤
            self.logger.info(f"[条件13] 价格和24小时数据筛选")
            price_valid = current_price <= 100
            change_valid = price_change_percent >= 8
            volume_valid = quote_volume >= 1_000_000
            daily_filter_valid = price_valid and change_valid and volume_valid
            
            self.logger.info(f"[条件13] 结果: {'通过' if daily_filter_valid else '未通过'} - "
                           f"价格 {current_price:.2f} ({'≤100' if price_valid else '>100'}), "
                           f"24h涨幅 {price_change_percent:.2f}% ({'≥8%' if change_valid else '<8%'}), "
                           f"24h成交量 {quote_volume:.0f} ({'≥100万' if volume_valid else '<100万'} USDT)")
            
            if not daily_filter_valid:
                self.logger.info(f"[总体结果] {symbol} 未通过24小时数据筛选，跳过处理")
                return False
                
            # 14. 重新检查突破条件（使用最新的价格），只检查做多条件
            self.logger.info(f"[条件14] 突破筛选（当前价格>前高）")
            breakout_valid = current_price > high
            self.logger.info(f"[条件14] 结果: {'通过' if breakout_valid else '未通过'} - "
                           f"当前价格 {current_price:.4f} {'>' if breakout_valid else '<='} 前高 {high:.4f}")
            
            if not breakout_valid:
                self.logger.info(f"[总体结果] {symbol} 未通过突破筛选，跳过处理")
                return False
                
            # 15. 检查杠杆模式
            self.logger.info(f"[条件15] 检查杠杆模式")
            lev_cfg = self.cfg['leverage']
            isolated_mode = lev_cfg['mode'] == 'isolated'
            self.logger.info(f"[条件15] 结果: {'通过' if isolated_mode else '未通过'} - "
                           f"当前模式 {lev_cfg['mode']} ({'逐仓' if isolated_mode else '全仓'})")
            
            if not isolated_mode:
                self.logger.info(f"[总体结果] {symbol} 杠杆模式不是逐仓，跳过处理")
                return False
                
            # 16. 设置杠杆倍数并检查结果
            self.logger.info(f"[条件16] 设置杠杆倍数")
            lev = lev_cfg['target_leverage']
            leverage_set = self.trader.set_leverage(symbol, lev)
            self.logger.info(f"[条件16] 结果: {'通过' if leverage_set else '未通过'} - "
                           f"设置杠杆 {lev}x {'成功' if leverage_set else '失败'}")
            
            if not leverage_set:
                self.logger.info(f"[总体结果] {symbol} 设置杠杆失败，跳过处理")
                return False
            
            # 17. 检查最大持仓数限制
            self.logger.info(f"[条件17] 检查最大持仓数限制")
            max_positions = self.cfg.get('max_positions', 5)
            position_check_passed = True
            if max_positions is not None:
                current_positions = self.trader.get_all_positions()
                positions_count = len(current_positions)
                position_check_passed = positions_count < max_positions
                self.logger.info(f"[条件17] 结果: {'通过' if position_check_passed else '未通过'} - "
                               f"当前持仓数 {positions_count}/{max_positions}")
            else:
                self.logger.info(f"[条件17] 结果: 通过 - 未设置最大持仓数限制")
            
            if not position_check_passed:
                self.logger.info(f"[总体结果] {symbol} 超过最大持仓数限制，跳过处理")
                return False
            
            # 18. 计算开仓数量和资金限制
            self.logger.info(f"[条件18] 计算开仓数量和资金限制")
            capital_cfg = self.cfg['capital']
            compounding_cfg = self.cfg.get('compounding', {'enabled': False})
            
            # 获取总资金
            total_balance = self.trader.get_total_balance()
            self.logger.info(f"[条件18] 总资金: {total_balance:.2f} USDT")
            
            if compounding_cfg.get('enabled', False):
                # 使用复利模式
                # 计算最大开仓金额：≤20 USDT 且 ≤20% 总本金
                max_open_usd = min(
                    20,  # 固定20 USDT上限
                    total_balance * 0.2,  # 20%总本金
                    capital_cfg['day_capital_usd']  # 配置文件中的每日限额
                )
                position_size = max_open_usd / current_price  # 按当前总资金计算仓位
                
                # 记录复利信息
                self.logger.info(f"复利模式 - 总资金: {total_balance:.2f} USDT, "
                               f"最大开仓: {max_open_usd:.2f} USDT, "
                               f"本次开仓: {position_size:.6f} {symbol} (价格: {current_price:.4f})")
            else:
                # 使用固定资金模式
                # 计算最大开仓金额：≤20 USDT 且 ≤20% 总本金
                max_open_usd = min(
                    20,  # 固定20 USDT上限
                    total_balance * 0.2,  # 20%总本金
                    capital_cfg['day_capital_usd']  # 配置文件中的每日限额
                )
                position_size = max_open_usd / current_price  # 使用固定资金
                self.logger.info(f"固定资金模式 - "
                               f"总资金: {total_balance:.2f} USDT, "
                               f"最大开仓: {max_open_usd:.2f} USDT, "
                               f"本次开仓: {position_size:.6f} {symbol} (价格: {current_price:.4f})")
            
            # 19. 检查当日开仓限制
            self.logger.info(f"[条件19] 检查当日开仓限制")
            daily_limit_check = self.today_opened_usd + max_open_usd <= capital_cfg['day_capital_usd']
            self.logger.info(f"[条件19] 结果: {'通过' if daily_limit_check else '未通过'} - "
                           f"已开仓 {self.today_opened_usd:.2f} + 本次开仓 {max_open_usd:.2f} = {self.today_opened_usd + max_open_usd:.2f} "
                           f"({'<=' if daily_limit_check else '>'} 限额 {capital_cfg['day_capital_usd']:.2f})")
            
            if not daily_limit_check:
                self.logger.info(f"[总体结果] {symbol} 超过当日开仓限额，跳过处理")
                return False
                
            # 所有条件都满足，准备开仓
            self.logger.info(f"[开仓决策] {symbol} 所有筛选条件均已通过，准备开仓")
            self.logger.info(f"[开仓决策] 当前价格 {current_price:.4f}, 前高 {high:.4f}")
            
            # 20. 根据突破方向开仓（只做多，不开空）
            order = None
            if current_price > high and pos_amt == 0:  # 突破新高，开多
                self.logger.info(f"[开仓执行] 确认突破条件 - 当前价格 {current_price:.4f} > 前高 {high:.4f}")
                
                # 再次检查是否超过当日开仓限额（防止并发问题）
                if self.today_opened_usd + max_open_usd > capital_cfg['day_capital_usd']:
                    self.logger.warning(f"当日开仓已达上限 {capital_cfg['day_capital_usd']:.2f} USDT，跳过")
                    self.logger.info(f"[总体结果] {symbol} 因并发问题超过当日开仓限额，跳过处理")
                    return False
                    
                self.logger.info(f"[开仓执行] 执行开多仓操作 - 数量 {position_size:.6f}")
                order = self.trader.place_order(
                    symbol=symbol,
                    side='BUY',
                    order_type='MARKET',
                    quantity=position_size,  # 使用正确计算的数量
                )
                if order and 'orderId' in order:
                    self.today_opened_usd += max_open_usd
                    self.logger.info(f"[突破新高] {symbol} 新币≤90天，24h涨幅+{price_change_percent:.0f}%，5m量{vol_ratio:.1f}×，得分89 → 实际开仓{max_open_usd:.2f}USDT → ATR3×止损 → 成功，订单ID: {order['orderId']}")
                    
                    # 开仓成功后立即设置止损订单
                    self._place_stop_loss_order(symbol, position_size, current_price)
                    self.logger.info(f"[总体结果] {symbol} 成功开多仓")
                    return True  # 成功开仓
                elif order:
                    self.logger.error(f"[突破新高] {symbol} 开多仓失败，订单响应异常: {order}")
                    self.logger.info(f"[总体结果] {symbol} 开多仓失败")
                else:
                    self.logger.error(f"[突破新高] {symbol} 开多仓失败")
                    self.logger.info(f"[总体结果] {symbol} 开多仓失败")
                    
            else:
                self.logger.info(f"[开仓执行] 未满足开仓条件 - 当前价格 {current_price:.4f} vs 前高 {high:.4f}")
                self.logger.info(f"[总体结果] {symbol} 不满足开仓条件")
            
            # 如果执行到这里，说明满足了所有条件但没有开仓（可能因为其他原因）
            self.logger.info(f"[总体结果] {symbol} 满足条件但未开仓")
            return True  # 满足条件但未开仓
            
        except Exception as e:
            self.logger.error(f"处理{symbol}时发生错误: {str(e)}")
            return False
            
    def run_forever(self):
        """持续运行策略"""
        self.logger.info("[OK] 策略开始运行...")
        
        while True:
            try:
                # 获取所有永续合约
                futures = self.trader.get_all_futures()
                self.logger.info(f"[OK] 扫描范围：全部{len(futures)}个永续合约")
                
                # 统计信息
                valid_coins_count = 0  # 真正符合条件的币数
                processed_coins_count = 0  # 处理的币数
                
                # 处理每个交易对
                for symbol in futures:
                    result = self.process_symbol(symbol)
                    processed_coins_count += 1
                    
                    # 只有真正符合条件并处理成功的币才计数
                    if result:
                        valid_coins_count += 1
                        
                    # 添加延迟以避免API限制
                    time.sleep(0.1)
                
                self.logger.info(f"本轮处理完成: 处理了 {processed_coins_count} 个币, 符合条件 {valid_coins_count} 个")
                
                # 等待一段时间后继续
                time.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                self.logger.error(f"策略运行时发生错误: {str(e)}")
                time.sleep(60)  # 出错后等待1分钟继续

if __name__ == "__main__":
    # 这里可以添加测试代码
    pass