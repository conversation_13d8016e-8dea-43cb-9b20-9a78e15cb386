#!/usr/bin/env python3
"""
精英策略升级脚本
一键将现有策略升级为精英版本
"""

import json
import shutil
import os
from datetime import datetime

def upgrade_strategy():
    """升级策略"""
    print("🚀 启动精英策略升级...")
    
    # 备份原配置
    if os.path.exists('config.json'):
        backup_name = f'config_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        shutil.copy('config.json', backup_name)
        print(f"✅ 已备份原配置: {backup_name}")
    
    # 应用精英配置
    with open('elite_config.json', 'r', encoding='utf-8') as f:
        elite_config = json.load(f)
    
    with open('config.json', 'w', encoding='utf-8') as f:
        json.dump(elite_config, f, indent=2, ensure_ascii=False)
    
    print("✅ 精英配置已应用")
    print("📋 下一步：")
    print("   1. 检查config.json中的API密钥")
    print("   2. 运行: python elite_validation_suite.py")
    print("   3. 回测通过后，以0.1x杠杆启动")

if __name__ == "__main__":
    upgrade_strategy()
