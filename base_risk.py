class BaseRisk:
    def __init__(self, config):
        self.max_loss = config['max_single_loss_usd']
        self.max_daily = config['base_risk']['max_daily_loss_usd']
        self.today_pnl = 0.0

    def check_order(self, symbol, qty, price):
        notional = qty * price
        if notional > self.max_loss * 10:          # 单值保护
            raise ValueError("单值超限")
        return True

    def update_pnl(self, pnl):
        self.today_pnl += pnl
        if self.today_pnl < -self.max_daily:
            raise RuntimeError("日亏超限，当日停止")

__all__ = ['BaseRisk']