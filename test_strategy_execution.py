#!/usr/bin/env python3
"""
测试策略执行逻辑 - 选择一个币并模拟完整交易流程
"""

import json
import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_strategy_execution():
    try:
        print("=== 测试策略执行逻辑 ===")
        
        # 导入必要的模块
        from day_breakout_strategy import DayBreakoutStrategy
        from binance_trader import BinanceTrader
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        
        # 创建策略实例
        strategy = DayBreakoutStrategy(trader, config)
        
        # 获取所有交易对
        all_futures = trader.get_all_futures()
        print(f"总共获取到 {len(all_futures)} 个交易对")
        
        # 选择一个交易对进行完整测试
        if all_futures:
            symbol = all_futures[0]  # 选择第一个交易对
            print(f"\n=== 测试交易对: {symbol} ===")
            
            # 执行策略处理逻辑
            print("执行策略处理...")
            strategy.process_symbol(symbol)
            print("策略处理完成")
            
            # 验证各个组件是否正常工作
            print("\n=== 验证组件功能 ===")
            
            # 1. 验证新币检查
            is_new = strategy._is_new_coin(symbol)
            print(f"新币检查: {is_new}")
            
            # 2. 验证杠杆检查
            has_leverage = strategy._has_open_leverage(symbol)
            print(f"杠杆检查: {has_leverage}")
            
            # 3. 验证ATR计算
            atr_value = strategy._calculate_atr_3x(symbol)
            print(f"ATR 3倍值: {atr_value:.2f}%")
            
            # 4. 验证获取K线数据
            klines = trader.get_klines(symbol, interval='5m', limit=48)
            print(f"获取K线数据: {len(klines) if klines else 0} 根")
            
            # 5. 验证获取当前价格
            ticker = trader.get_symbol_ticker(symbol)
            if ticker and 'price' in ticker:
                current_price = float(ticker['price'])
                print(f"当前价格: {current_price:.4f} USDT")
            else:
                print("获取当前价格失败")
            
            # 6. 验证获取24小时统计
            ticker_24h = trader.get_ticker(symbol)
            if ticker_24h:
                price_change = ticker_24h.get('priceChangePercent', 'N/A')
                volume = ticker_24h.get('quoteVolume', 'N/A')
                print(f"24小时涨幅: {price_change}%")
                print(f"24小时成交量: {volume} USDT")
            else:
                print("获取24小时统计失败")
            
            # 7. 验证获取持仓信息
            position = trader.get_position(symbol)
            if position:
                pos_amt = position.get('positionAmt', 'N/A')
                entry_price = position.get('entryPrice', 'N/A')
                print(f"当前持仓: {pos_amt}")
                print(f"持仓均价: {entry_price}")
            else:
                print("获取持仓信息失败")
            
            # 8. 验证获取总资金
            total_balance = trader.get_total_balance()
            print(f"总资金: {total_balance:.2f} USDT")
            
            # 9. 验证获取所有持仓
            all_positions = trader.get_all_positions()
            print(f"所有持仓数量: {len(all_positions)}")
            
            print("\n=== 测试结果 ===")
            print("✓ 策略执行逻辑验证完成")
            print("✓ 所有组件功能正常")
            print("✓ 策略逻辑没有问题，可以正常执行")
            
        else:
            print("没有获取到交易对，无法进行测试")
                
    except Exception as e:
        print(f"测试策略执行时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_strategy_execution()