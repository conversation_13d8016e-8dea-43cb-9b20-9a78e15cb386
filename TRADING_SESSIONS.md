# 交易时段配置说明

## 概述

本策略支持多种交易时段配置，包括日盘、夜盘以及同时开启日盘和夜盘交易。用户可以根据自己的交易偏好和市场情况灵活配置。

## 配置选项

### 1. 日盘交易（默认）
- 交易时间：北京时间09:30-16:00
- UTC时间：01:30-08:00

### 2. 夜盘交易
- 交易时间：北京时间21:30-04:00
- UTC时间：13:30-20:00

### 3. 日盘+夜盘双时段交易
- 同时在日盘和夜盘时段进行交易

### 4. 动态自适应窗口
- 根据历史波动率自动选择最佳交易时段

## 配置方法

在[config.json](file://d:\roll\day_breakout\day_breakout_final\config.json)中通过[window_type](file://d:\roll\day_breakout\day_breakout_final\day_breakout_strategy.py#L36-L36)参数配置交易时段：

```json
"window_type": "day"    // 日盘交易
"window_type": "night"  // 夜盘交易
"window_type": "both"   // 日盘+夜盘双时段交易
"window_type": "adaptive" // 动态自适应窗口
```

## 实现细节

### 多时段支持

在[day_breakout_strategy.py](file://d:\roll\day_breakout\day_breakout_final\day_breakout_strategy.py)中实现了多时段支持：

1. [get_trade_sessions()](file://d:\roll\day_breakout\day_breakout_final\day_breakout_strategy.py#L78-L107)方法返回当前配置的交易时段列表
2. [is_in_any_trade_session()](file://d:\roll\day_breakout\day_breakout_final\day_breakout_strategy.py#L109-L125)方法检查当前时间是否在任何交易时段内

### 双时段交易逻辑

当[window_type](file://d:\roll\day_breakout\day_breakout_final\day_breakout_strategy.py#L36-L36)设置为"both"时：
1. 策略会在日盘时段（09:30-16:00）检查交易机会
2. 策略会在夜盘时段（21:30-04:00）检查交易机会
3. 在非交易时段暂停开新仓，但保持现有持仓

## 使用建议

### 1. 日盘交易
适合希望跟随传统股市交易时间的投资者

### 2. 夜盘交易
适合希望捕捉加密货币市场夜间波动的投资者

### 3. 双时段交易
适合希望最大化交易机会的投资者，可以同时捕捉日间和夜间的机会

### 4. 动态自适应窗口
适合希望根据市场波动性自动调整交易时段的投资者

## 配置示例

### 启用双时段交易
```json
"window_type": "both"
```

### 启用夜盘交易
```json
"window_type": "night"
```

### 启用日盘交易
```json
"window_type": "day"
```

### 启用动态自适应窗口
```json
"window_type": "adaptive"
"dynamic_window": true
```

## 注意事项

1. 双时段交易会增加交易机会，但也可能增加风险
2. 日终复利操作仍然在每日23:50-23:59之间执行一次
3. 最大持仓数量限制对所有时段都有效
4. 风险控制机制对所有时段都有效