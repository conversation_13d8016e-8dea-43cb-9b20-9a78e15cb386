#!/usr/bin/env python3
from detailed_day_breakout_strategy import DayBreakoutStrategy
from binance_trader import BinanceTrader
from logger_config import setup_logger, devil_banner
import json
import pathlib
import socket
import requests

def auto_proxy(config):
    """大陆IP→启用代理，阿里云/海外→关闭"""
    try:
        # 测试能否直连 Binance REST
        resp = requests.get('https://fapi.binance.com/fapi/v1/time', timeout=2)
        if resp.status_code == 200:
            config['network']['proxy']['enabled'] = False
            setup_logger().info("直连测试成功，禁用代理")
            return
    except Exception as e:
        setup_logger().error(f"直连测试失败: {e}")
        pass
    # 连不上→默认启用代理
    config['network']['proxy']['enabled'] = True
    setup_logger().info("直连测试失败，启用代理")

def main():
    logger = setup_logger()
    cfg = json.loads(pathlib.Path('config.json').read_text())
    logger.info(f"配置文件加载完成，代理设置: {cfg['network']['proxy']}")
    auto_proxy(cfg)   # 自动选代理
    logger.info(f"自动代理设置完成，代理状态: {cfg['network']['proxy']['enabled']}")
    trader = BinanceTrader(cfg)
    strategy = DayBreakoutStrategy(trader, cfg)
    
    # 检查是否启用魔鬼级日志
    if cfg.get('logger', {}).get('startup_banner', False):
        devil_banner(cfg, trader)
    else:
        logger.info("Day-Breakout 620-line strategy started")
    
    strategy.run_forever()

if __name__ == '__main__':
    main()