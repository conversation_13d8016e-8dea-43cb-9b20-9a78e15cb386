# 零追加自动降杆策略使用指南

## 🎯 核心概念

**零追加**策略的核心理念：**只减仓，不加钱**。通过自动减仓将杠杆控制在安全范围内，永远不需要追加保证金。

## 🔧 新增功能

### 1. 自动降杆函数 (`_reduce_only_down_to_lev`)

```python
def _reduce_only_down_to_lev(self, symbol, target_lev=2.0):
    """
    零追加自动降杆函数 - 只减仓，不加钱
    把杠杆压到 target_lev，通过减仓实现
    """
```

**工作原理**：
- 计算当前实际杠杆倍数
- 如果超过目标值，自动计算需要减仓的数量
- 执行市价减仓，只减少仓位不增加保证金

### 2. 触发机制

在每个K线周期和持仓管理时自动检查：
```python
# 零追加自动降杆检查
target_lev = self.cfg.get('leverage', {}).get('target_leverage', 2.0)
self._reduce_only_down_to_lev(symbol, target_lev)
```

## 📊 配置参数

### 基础配置 (`config.json`)

```json
{
  "leverage": {
    "mode": "isolated",
    "target_leverage": 2,  // 目标杠杆倍数
    "cap_at_max": true
  },
  "trading": {
    "zero_margin": {
      "enabled": true,     // 启用零追加保护
      "target_leverage": 2.0,  // 强制降杆目标
      "max_leverage": 3.0      // 最大允许杠杆
    }
  }
}
```

## 🎮 使用示例

### 场景1：正常开仓
- 初始：25U保证金，50U名义价值，杠杆=2.0×
- 结果：无需减仓，杠杆达标

### 场景2：浮盈加仓后
- 初始：25U保证金，80U名义价值，杠杆=3.2×
- 动作：自动减仓22.5U
- 结果：杠杆回到2.0×，钱包25U不变

### 场景3：极端情况
- 初始：25U保证金，100U名义价值，杠杆=4.0×
- 动作：自动减仓37.5U
- 结果：杠杆回到2.0×，钱包25U不变

## ⚡ 启动命令

```bash
# 使用零追加配置
python day_breakout_strategy.py --config zero_margin_config.json

# 或者修改现有配置
python day_breakout_strategy.py --config config.json --target-leverage 2.0
```

## 🛡️ 安全特性

1. **只减仓保护**：所有订单都带`reduce_only=True`标志
2. **异常处理**：错误日志记录，不影响主策略运行
3. **精度控制**：保留3位小数，避免过度减仓
4. **实时检查**：每根K线都会重新评估杠杆

## 📈 日志示例

```
[零追加降杆] BTCUSDT 减=0.002 杠杆=3.20→2.00
[零追加降杆] ETHUSDT 减=0.015 杠杆=4.50→2.00
```

## 🔍 测试验证

运行测试脚本验证功能：
```bash
python test_zero_margin.py
```

## 💡 最佳实践

1. **建议目标杠杆**：2-3倍，根据风险承受能力调整
2. **监控频率**：每根K线检查一次，确保及时响应
3. **资金效率**：减仓释放的资金可用于开新仓
4. **风险控制**：配合止损止盈，形成完整风控体系

## ⚠️ 注意事项

- 零追加策略适合**长期稳定盈利**的交易者
- 建议先在**模拟盘**测试验证
- 关注**资金费率**影响，避免在资金费率前减仓
- 定期检查**API限制**，确保有足够的下单额度

## 🎯 总结

零追加策略将传统"追加保证金"的风险管理方式，转变为"事前减仓"的主动风控。通过技术手段确保杠杆始终处于安全范围，真正实现"只动仓位，不动钱包"的交易理念。