#!/usr/bin/env python3
"""
测试新币判断逻辑的脚本
"""

import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_new_coins():
    try:
        # 导入必要的模块
        from day_breakout_strategy import DayBreakoutStrategy
        from binance_trader import BinanceTrader
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        
        # 创建策略实例
        strategy = DayBreakoutStrategy(trader, config)
        
        # 获取所有交易对
        all_futures = trader.get_all_futures()
        print(f"总共获取到 {len(all_futures)} 个交易对")
        
        # 检查前20个交易对的新币状态
        new_coins = []
        old_coins = []
        
        for symbol in all_futures[:20]:
            is_new = strategy._is_new_coin(symbol)
            has_leverage = strategy._has_open_leverage(symbol)
            
            print(f"{symbol}: 新币={is_new}, 已开放杠杆={has_leverage}")
            
            if is_new:
                new_coins.append(symbol)
            else:
                old_coins.append(symbol)
                
        print(f"\n新币数量: {len(new_coins)}")
        print(f"老币数量: {len(old_coins)}")
        print(f"新币列表: {new_coins}")
        
        # 详细分析一个新币的K线数据
        if new_coins:
            symbol = new_coins[0]
            print(f"\n=== 详细分析新币 {symbol} ===")
            klines = trader.get_klines(symbol, interval='1d', limit=100)
            print(f"获取到 {len(klines)} 根日K线")
            
            # 分析最近几根K线
            if klines:
                for i, kline in enumerate(klines[-5:]):
                    print(f"K线 {i+1}: {kline}")
                    
    except Exception as e:
        print(f"测试新币判断时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_new_coins()