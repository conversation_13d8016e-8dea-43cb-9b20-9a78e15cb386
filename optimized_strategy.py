#!/usr/bin/env python3
"""
优化版策略 - 放宽筛选条件以提高选币概率
"""

import json
import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')

def analyze_strategy_filters():
    """分析策略筛选条件并提供优化建议"""
    print("=== 策略筛选条件分析 ===")
    
    filters = {
        "新币筛选 (≤90天)": {
            "当前条件": "上市时间≤90天",
            "建议优化": "可以适当放宽到≤120天以增加候选币数量"
        },
        "5分钟筛选": {
            "当前条件": "涨幅≥3% 且 成交量≥1.5×均量",
            "建议优化": "可以调整为涨幅≥2% 且 成交量≥1.2×均量"
        },
        "价格筛选": {
            "当前条件": "价格≤100 USDT",
            "建议优化": "保持不变，这个条件相对合理"
        },
        "24小时筛选": {
            "当前条件": "涨幅≥8% 且 成交量≥100万USDT",
            "建议优化": "可以调整为涨幅≥5% 且 成交量≥50万USDT"
        },
        "突破筛选": {
            "当前条件": "当前价格>前高",
            "建议优化": "保持不变，这是核心突破条件"
        }
    }
    
    for filter_name, filter_info in filters.items():
        print(f"\n{filter_name}:")
        print(f"  当前条件: {filter_info['当前条件']}")
        print(f"  建议优化: {filter_info['建议优化']}")
    
    print("\n=== 优化建议 ===")
    print("1. 适当放宽新币时间限制，从90天增加到120天")
    print("2. 降低5分钟筛选条件，提高选币概率")
    print("3. 适度降低24小时涨幅和成交量要求")
    print("4. 添加更多调试日志，便于分析筛选过程")
    print("5. 考虑添加时间窗口，避免在市场波动较小的时间段过度交易")

def create_relaxed_config():
    """创建放宽条件的配置文件"""
    relaxed_config = {
        "strategy_filters": {
            "new_coin_days": 120,  # 新币时间从90天放宽到120天
            "five_min_change_pct": 2.0,  # 5分钟涨幅从3%降低到2%
            "five_min_volume_ratio": 1.2,  # 5分钟成交量比率从1.5降低到1.2
            "price_upper_limit": 100,  # 价格上限保持100 USDT不变
            "daily_change_pct": 5.0,  # 24小时涨幅从8%降低到5%
            "daily_volume_usd": 500000  # 24小时成交量从100万降低到50万
        }
    }
    
    print("\n=== 放宽条件配置 ===")
    for key, value in relaxed_config["strategy_filters"].items():
        print(f"{key}: {value}")

def main():
    """主函数"""
    print("策略优化分析工具")
    analyze_strategy_filters()
    create_relaxed_config()
    
    print("\n=== 使用建议 ===")
    print("1. 修改 [_is_new_coin](file:///d:/roll/day_breakout/day_breakout_strategy.py#L41-L51) 方法中的天数限制")
    print("2. 调整 [process_symbol](file:///d:/roll/day_breakout/day_breakout_strategy.py#L133-L346) 方法中的筛选条件")
    print("3. 增加调试日志输出，便于分析筛选过程")
    print("4. 考虑在不同市场环境下使用不同的筛选条件")

if __name__ == "__main__":
    main()