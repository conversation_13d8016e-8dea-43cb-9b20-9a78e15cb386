#!/usr/bin/env python3
"""
测试强平价格检查功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import json
from datetime import datetime
import logging

class MockTrader:
    """模拟交易接口"""
    def __init__(self):
        self.positions = {}
        
    def get_position(self, symbol):
        return self.positions.get(symbol, None)
        
    def place_order(self, **kwargs):
        return {"orderId": f"STOP_{kwargs['symbol']}_{int(datetime.now().timestamp())}"}

class MockATRStrategy:
    """模拟ATR策略，包含强平价格检查"""
    def __init__(self):
        self.cfg = {
            'trading': {
                'stop_loss_type': 'atr',
                'fixed_stop_loss_pct': 3,
                'atr_multiplier': 3
            }
        }
        self.trader = MockTrader()
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def _calculate_atr_3x(self, symbol):
        """模拟ATR计算"""
        atr_values = {
            'BTCUSDT': 2.5,
            'ETHUSDT': 3.2,
            'SOLUSDT': 4.1,
            'ADAUSDT': 2.8,
            'XRPUSDT': 3.5
        }
        return atr_values.get(symbol, 3.0)
        
    def _place_stop_loss_order(self, symbol, quantity, entry_price):
        """复制实际的止损设置逻辑"""
        try:
            # 从配置文件获取止损类型和参数
            stop_loss_type = self.cfg.get('trading', {}).get('stop_loss_type', 'fixed')
            
            # 获取当前持仓信息以检查强平价格
            curr_pos = self.trader.get_position(symbol)
            if not curr_pos:
                self.logger.error(f"[止损] 无法获取{symbol}持仓信息")
                return None
                
            # 获取强平价格
            liquidation_price = float(curr_pos.get('liquidationPrice', 0))
            if liquidation_price <= 0:
                self.logger.warning(f"[止损] {symbol} 无法获取有效强平价格，使用默认风险控制")
                liquidation_price = entry_price * 0.5  # 保守估计
            
            self.logger.info(f"[风险控制] {symbol} 强平价格: {liquidation_price:.4f}, 入场价格: {entry_price:.4f}")
            
            if stop_loss_type == "atr":
                # ATR止损模式
                atr_3x_pct = self._calculate_atr_3x(symbol)
                if atr_3x_pct <= 0:
                    self.logger.warning(f"{symbol} ATR计算失败，使用固定止损")
                    atr_3x_pct = self.cfg.get('trading', {}).get('fixed_stop_loss_pct', 3)
                else:
                    atr_multiplier = self.cfg.get('trading', {}).get('atr_multiplier', 3)
                    atr_3x_pct = atr_3x_pct * (atr_multiplier / 3)  # 根据配置调整ATR倍数
                    self.logger.info(f"{symbol} 使用ATR止损，ATR倍数: {atr_multiplier}x")
                
                stop_loss_pct = atr_3x_pct
            else:
                # 固定止损模式（默认）
                stop_loss_pct = self.cfg.get('trading', {}).get('fixed_stop_loss_pct', 3)
                self.logger.info(f"{symbol} 使用固定止损，止损百分比: {stop_loss_pct}%")
            
            # 计算止损价格
            stop_price = entry_price * (1 - stop_loss_pct / 100)
            
            # 🔥 关键：确保止损价格高于强平价格
            safety_margin = 0.5  # 0.5%的安全边际
            min_safe_stop = liquidation_price * (1 + safety_margin / 100)
            
            if stop_price <= liquidation_price:
                # 如果止损价格低于或等于强平价格，调整止损价格
                adjusted_stop_price = min_safe_stop
                adjusted_stop_pct = ((entry_price - adjusted_stop_price) / entry_price) * 100
                
                self.logger.warning(
                    f"[风险控制] {symbol} 止损价格调整: "
                    f"原止损价 {stop_price:.4f} ≤ 强平价 {liquidation_price:.4f}, "
                    f"调整为 {adjusted_stop_price:.4f} (止损比例 {adjusted_stop_pct:.2f}%)"
                )
                
                stop_price = adjusted_stop_price
                stop_loss_pct = adjusted_stop_pct
            else:
                self.logger.info(f"[风险控制] {symbol} 止损价格检查通过: {stop_price:.4f} > {liquidation_price:.4f}")
            
            # 设置止损订单
            order = self.trader.place_order(
                symbol=symbol,
                side='SELL',
                order_type='STOP_MARKET',
                quantity=quantity,
                stop_price=stop_price,
                reduce_only=True
            )
            
            if order and 'orderId' in order:
                stop_order_id = order['orderId']
                self.logger.info(
                    f"[✅止损设置成功] {symbol} "
                    f"止损@{stop_price:.4f} (比例{stop_loss_pct:.2f}%), "
                    f"强平价@{liquidation_price:.4f}, 订单ID: {stop_order_id}"
                )
                return stop_order_id
            else:
                self.logger.error(f"[❌止损] {symbol} 设置止损订单失败")
                return None
                
        except Exception as e:
            self.logger.error(f"设置止损订单时出错: {e}")
            return None

def test_liquidation_check():
    """测试强平价格检查功能"""
    strategy = MockATRStrategy()
    
    # 测试用例：不同场景下的强平价格检查
    test_cases = [
        {
            'symbol': 'BTCUSDT',
            'entry_price': 50000,
            'liquidation_price': 45000,
            'expected_adjusted': False,  # 止损价正常，无需调整
            'description': '正常情况：止损价高于强平价'
        },
        {
            'symbol': 'ETHUSDT',
            'entry_price': 3000,
            'liquidation_price': 2850,
            'expected_adjusted': False,   # 止损价2904 > 强平价2850，无需调整
            'description': '危险情况：止损价接近强平价但仍在安全范围'
        },
        {
            'symbol': 'SOLUSDT',
            'entry_price': 100,
            'liquidation_price': 99.5,
            'expected_adjusted': True,   # 止损价非常接近强平价
            'description': '临界情况：止损价接近强平价'
        },
        {
            'symbol': 'ADAUSDT',
            'entry_price': 0.5,
            'liquidation_price': 0.45,
            'expected_adjusted': False,  # 止损价安全
            'description': '低价币：止损价安全'
        }
    ]
    
    print("🧪 开始测试强平价格检查功能...")
    print("=" * 60)
    
    results = []
    
    for case in test_cases:
        symbol = case['symbol']
        entry_price = case['entry_price']
        liquidation_price = case['liquidation_price']
        
        # 设置模拟持仓信息
        strategy.trader.positions[symbol] = {
            'symbol': symbol,
            'liquidationPrice': str(liquidation_price),
            'positionAmt': '1.0'
        }
        
        print(f"\n📊 {case['description']}")
        print(f"   入场价格: ${entry_price}")
        print(f"   强平价格: ${liquidation_price}")
        
        # 计算理论止损价格（ATR 3倍）
        atr_pct = strategy._calculate_atr_3x(symbol)
        theoretical_stop = entry_price * (1 - atr_pct / 100)
        print(f"   理论止损: ${theoretical_stop:.4f} (ATR {atr_pct}%)")
        
        # 执行止损设置
        result = strategy._place_stop_loss_order(symbol, 1.0, entry_price)
        
        # 检查结果
        adjusted = theoretical_stop <= liquidation_price
        passed = adjusted == case['expected_adjusted']
        
        results.append({
            'symbol': symbol,
            'case': case['description'],
            'passed': passed,
            'adjusted': adjusted
        })
        
        print(f"   测试结果: {'✅ 通过' if passed else '❌ 失败'}")
        print("-" * 40)
    
    # 汇总结果
    print("\n📋 测试汇总:")
    print("=" * 60)
    passed_count = sum(1 for r in results if r['passed'])
    total_count = len(results)
    
    for result in results:
        status = "✅" if result['passed'] else "❌"
        adjusted = "(已调整)" if result['adjusted'] else "(无需调整)"
        print(f"{status} {result['symbol']} - {result['case']} {adjusted}")
    
    print(f"\n🎯 总计: {passed_count}/{total_count} 测试通过")
    
    if passed_count == total_count:
        print("🎉 所有测试通过！强平价格检查功能正常工作")
    else:
        print("⚠️  部分测试失败，需要检查逻辑")
    
    return passed_count == total_count

if __name__ == "__main__":
    test_liquidation_check()