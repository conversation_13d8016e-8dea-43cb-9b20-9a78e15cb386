#!/usr/bin/env python3
"""
语法检查脚本
"""

import os
import sys
import subprocess

def check_syntax(file_path):
    """检查单个文件的语法"""
    try:
        result = subprocess.run([sys.executable, '-m', 'py_compile', file_path], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✓ {file_path} 语法正确")
            return True
        else:
            print(f"✗ {file_path} 语法错误:")
            print(result.stderr)
            return False
    except subprocess.TimeoutExpired:
        print(f"✗ {file_path} 检查超时")
        return False
    except Exception as e:
        print(f"✗ {file_path} 检查失败: {e}")
        return False

def main():
    """主函数"""
    # 获取项目根目录
    project_root = os.path.dirname(os.path.abspath(__file__))
    
    # 定义要检查的Python文件
    python_files = [
        'day_breakout_strategy.py',
        'binance_trader.py',
        'http_client.py',
        'logger_config.py',
        'start_day_breakout.py',
        'base_risk.py',
        'check_positions.py',
        'test_enhanced_strategy.py',
        'fixed_test_enhanced_strategy.py',
        'verify_fix.py',
        'simple_test.py'
    ]
    
    # 检查每个文件的语法
    all_correct = True
    for file_name in python_files:
        file_path = os.path.join(project_root, file_name)
        if os.path.exists(file_path):
            if not check_syntax(file_path):
                all_correct = False
        else:
            print(f"⚠ {file_path} 文件不存在")
    
    # 输出总结
    if all_correct:
        print("\n🎉 所有Python文件语法检查通过!")
    else:
        print("\n❌ 部分文件存在语法错误，请检查上述输出。")
    
    return all_correct

if __name__ == "__main__":
    main()