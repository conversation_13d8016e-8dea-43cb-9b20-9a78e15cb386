#!/usr/bin/env python3
"""
DayBreakout策略实现
"""

import json
import sys
import os
import time
import logging
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class DayBreakoutStrategy:
    def __init__(self, trader, config):
        self.trader = trader
        self.cfg = config
        self.logger = logging.getLogger(__name__)
        
        # 初始化每日统计
        self.decay_ref = datetime.utcnow()
        self.today_opened_usd = 0
        
    def reset_daily(self):
        """重置每日统计"""
        self.decay_ref = datetime.utcnow()
        self.today_opened_usd = 0
        self.logger.info("[OK] 北京00:00已重置")
        
    def _is_new_coin(self, symbol):
        """检查是否为新币（上市时间≤90天）"""
        try:
            # 获取交易对信息
            exchange_info = self.trader.http.get('/fapi/v1/exchangeInfo')
            if not exchange_info or 'symbols' not in exchange_info:
                return False
                
            for sym_info in exchange_info['symbols']:
                if sym_info['symbol'] == symbol:
                    # 获取上市时间
                    listing_time = sym_info.get('listingTime', 0)
                    if listing_time:
                        listing_date = datetime.fromtimestamp(listing_time / 1000)
                        days_since_listing = (datetime.utcnow() - listing_date).days
                        return days_since_listing <= 90
            return False
        except Exception as e:
            self.logger.error(f"检查新币时出错: {e}")
            return False
            
    def _has_open_leverage(self, symbol):
        """检查交易对是否已开放杠杆（只检查是否在有效交易对列表中）"""
        try:
            # 获取所有有效交易对
            futures = self.trader.get_all_futures()
            return symbol in futures
        except Exception as e:
            self.logger.error(f"检查杠杆时出错: {e}")
            return False
            
    def _calculate_atr_3x(self, symbol):
        """计算ATR 3倍值"""
        try:
            # 获取K线数据
            klines = self.trader.get_klines(symbol, interval='1d', limit=15)
            if not klines or len(klines) < 2:
                return 0
                
            # 计算ATR
            trs = []
            for i in range(1, len(klines)):
                high = float(klines[i][2])
                low = float(klines[i][3])
                prev_close = float(klines[i-1][4])
                tr = max(high - low, abs(high - prev_close), abs(low - prev_close))
                trs.append(tr)
                
            if not trs:
                return 0
                
            atr = sum(trs) / len(trs)
            current_price = float(klines[-1][4])
            atr_pct = (atr / current_price) * 100 if current_price > 0 else 0
            
            return atr_pct * 3  # 返回3倍ATR百分比
        except Exception as e:
            self.logger.error(f"计算ATR时出错: {e}")
            return 0
            
    def _place_stop_loss_order(self, symbol, quantity, entry_price):
        """设置止损订单并返回止损订单ID"""
        try:
            # 计算止损价格（3%固定止损）
            stop_loss_pct = self.cfg.get('stop_loss_pct', 3)
            stop_price = entry_price * (1 - stop_loss_pct / 100)
            
            # 设置止损订单
            order = self.trader.place_order(
                symbol=symbol,
                side='SELL',
                order_type='STOP_MARKET',
                quantity=quantity,
                stop_price=stop_price,
                reduce_only=True
            )
            
            if order and 'orderId' in order:
                stop_order_id = order['orderId']
                self.logger.info(f"[止损] {symbol} 设置止损订单成功 @{stop_price:.4f}, 止损订单ID: {stop_order_id}")
                return stop_order_id
            else:
                self.logger.error(f"[止损] {symbol} 设置止损订单失败")
                return None
        except Exception as e:
            self.logger.error(f"设置止损订单时出错: {e}")
            return None

    def _check_order_filled(self, symbol, order_id):
        """检查订单是否已完全成交"""
        try:
            order_status = self.trader.http.get('/fapi/v1/order', {
                'symbol': symbol,
                'orderId': order_id
            })
            
            if order_status and 'status' in order_status:
                status = order_status['status']
                filled_qty = float(order_status.get('executedQty', 0))
                orig_qty = float(order_status.get('origQty', 0))
                
                self.logger.info(f"[订单检查] {symbol} 订单状态: {status}, 已成交: {filled_qty}/{orig_qty}")
                
                if status == 'FILLED' and filled_qty > 0:
                    # 获取实际成交价格
                    avg_price = float(order_status.get('avgPrice', 0))
                    if avg_price == 0:
                        # 如果avgPrice为0，使用当前价格
                        ticker = self.trader.get_symbol_ticker(symbol)
                        avg_price = float(ticker['price']) if ticker else 0
                    
                    return True, filled_qty, avg_price
                elif status == 'PARTIALLY_FILLED':
                    return False, filled_qty, 0
                else:
                    return False, 0, 0
            else:
                self.logger.error(f"[订单检查] 获取订单状态失败: {order_status}")
                return False, 0, 0
                
        except Exception as e:
            self.logger.error(f"检查订单状态时出错: {e}")
            return False, 0, 0

    def process_symbol(self, symbol):
        """处理单个交易对"""
        try:
            # 检查是否为新币且已开放杠杆
            is_new = self._is_new_coin(symbol)
            has_leverage = self._has_open_leverage(symbol)
            
            if not (is_new and has_leverage):
                return False  # 不是新币或未开放杠杆，跳过
                
            # 检查日期是否过期
            now = datetime.utcnow()
            if now > self.decay_ref + timedelta(days=1):
                self.reset_daily()
                
            # 获取当前交易时段
            trade = self.cfg['trade_session']
            curr_hour = (now.hour + 8) % 24  # 转为北京时间
            curr_minute = now.minute
            
            # ① 时间窗口检查
            start_hour = trade['start_hour'] 
            start_minute = trade['start_minute']
            end_hour = trade['end_hour']
            end_minute = trade['end_minute']
            
            # ② 转换为分钟来比较
            curr_time = curr_hour * 60 + curr_minute
            start_time = start_hour * 60 + start_minute
            end_time = end_hour * 60 + end_minute
            
            # ③ 跨夜处理
            if end_time < start_time:  # 跨夜交易时段
                if curr_time < end_time or curr_time >= start_time:
                    pass  # 在交易时段内
                else:
                    return False  # 不在交易时段
            else:  # 普通交易时段
                if curr_time < start_time or curr_time >= end_time:
                    return False  # 不在交易时段
            
            # 获取K线数据
            klines = self.trader.get_klines(symbol)
            if not klines:
                return False
            
            # 计算动态指标
            highs = [float(k[2]) for k in klines]
            lows = [float(k[3]) for k in klines]
            high = max(highs[:-1])  # 不包含当前K线
            low = min(lows[:-1])
            
            # 5m筛选：检查最近的5m K线涨幅和成交量
            vol_ratio = 0  # 初始化vol_ratio变量
            if len(klines) >= 2:
                # 获取最近两根K线的数据
                recent_kline = klines[-1]  # 最近的K线
                prev_kline = klines[-2]    # 前一根K线
                
                # 计算5m涨幅
                recent_open = float(recent_kline[1])
                recent_close = float(recent_kline[4])
                recent_change_pct = ((recent_close - recent_open) / recent_open) * 100
                
                # 计算成交量比率
                recent_vol = float(recent_kline[5])
                prev_vol = float(prev_kline[5])
                vol_ratio = recent_vol / prev_vol if prev_vol > 0 else 0
                
                # 5m筛选：涨幅≥3%且成交量≥1.5×均量
                if recent_change_pct < 3 or vol_ratio < 1.5:
                    # 添加调试信息
                    self.logger.debug(f"{symbol} 5m筛选未通过: 涨幅{recent_change_pct:.2f}% (要求≥3%), 成交量比率{vol_ratio:.2f}x (要求≥1.5x)")
                    return False  # 不符合条件，跳过
                
                self.logger.info(f"{symbol} 5m涨幅: {recent_change_pct:.2f}%, 成交量比率: {vol_ratio:.2f}x")
                
            # 获取当前价格和前一根K线的价格
            curr_price = float(klines[-1][4])  # 当前收盘价
            last_price = float(klines[-2][4])  # 上一根收盘价
            
            # 检查价格突破（提前检查，避免不必要的API调用）
            curr_pos = self.trader.get_position(symbol)
            pos_amt = float(curr_pos['positionAmt']) if curr_pos else 0
            
            # 如果已经有持仓，则进行持仓管理
            if pos_amt != 0:
                # 获取当前价格
                ticker = self.trader.get_symbol_ticker(symbol)
                if not ticker:
                    self.logger.error(f"获取{symbol}价格失败，跳过")
                    return False
                current_price = float(ticker['price'])
                
                # 获取持仓均价
                entry_price = float(curr_pos['entryPrice'])
                
                # 计算浮动盈亏百分比
                pnl_pct = (current_price - entry_price) / entry_price * 100
                if pos_amt < 0:  # 空仓
                    pnl_pct = -pnl_pct
                    
                # 止盈止损
                if pnl_pct <= -self.cfg['stop_loss_pct']:  # 止损
                    side = 'BUY' if pos_amt < 0 else 'SELL'
                    order = self.trader.place_order(
                        symbol=symbol,
                        side=side,
                        order_type='MARKET',
                        quantity=abs(pos_amt),
                        reduce_only=True
                    )
                    if order and 'orderId' in order:
                        self.logger.info(f"[止损] {symbol} 平仓 @{current_price:.4f}, 订单ID: {order['orderId']}")
                    elif order:
                        self.logger.error(f"[止损] {symbol} 平仓失败，订单响应异常: {order}")
                    else:
                        self.logger.error(f"[止损] {symbol} 平仓失败")
                        
                elif pnl_pct >= self.cfg['take_profit_pct']:  # 止盈
                    side = 'BUY' if pos_amt < 0 else 'SELL'
                    order = self.trader.place_order(
                        symbol=symbol,
                        side=side,
                        order_type='MARKET',
                        quantity=abs(pos_amt),
                        reduce_only=True
                    )
                    if order and 'orderId' in order:
                        self.logger.info(f"[止盈] {symbol} 平仓 @{current_price:.4f}, 订单ID: {order['orderId']}")
                    elif order:
                        self.logger.error(f"[止盈] {symbol} 平仓失败，订单响应异常: {order}")
                    else:
                        self.logger.error(f"[止盈] {symbol} 平仓失败")
                
                # 如果已经有持仓，不需要再检查开仓条件
                return True  # 已处理
            
            # 如果没有持仓，检查开仓条件
            # 再次获取当前价格用于开仓判断
            ticker = self.trader.get_symbol_ticker(symbol)
            if not ticker:
                self.logger.error(f"获取{symbol}价格失败，跳过")
                return False
            current_price = float(ticker['price'])
            
            # 获取24小时统计信息
            ticker_24h = self.trader.get_ticker(symbol)
            if not ticker_24h:
                self.logger.error(f"获取{symbol}24小时统计信息失败，跳过")
                return False
            
            # 获取24小时涨幅和成交量
            price_change_percent = float(ticker_24h.get('priceChangePercent', 0))
            quote_volume = float(ticker_24h.get('quoteVolume', 0))
            
            # 价格≤100 USDT + 24h涨幅≥8% + 量≥100万 USD过滤
            if current_price > 100 or price_change_percent < 8 or quote_volume < 1_000_000:
                # 添加调试信息
                reasons = []
                if current_price > 100:
                    reasons.append(f"价格{current_price:.2f} > 100")
                if price_change_percent < 8:
                    reasons.append(f"24h涨幅{price_change_percent:.2f}% < 8%")
                if quote_volume < 1_000_000:
                    reasons.append(f"24h成交量{quote_volume:.0f} < 1,000,000")
                self.logger.debug(f"{symbol} 24h筛选未通过: {', '.join(reasons)}")
                return False  # 不符合条件，跳过
            
            # 重新检查突破条件（使用最新的价格），只检查做多条件
            if not (current_price > high):
                self.logger.debug(f"{symbol} 突破筛选未通过: 当前价格{current_price:.4f} <= 前高{high:.4f}")
                return False  # 没有突破条件，直接返回
            
            # 设置杠杆
            lev_cfg = self.cfg['leverage']
            if lev_cfg['mode'] != 'isolated':
                self.logger.error("只允许逐仓模式")
                return False
                
            # 设置杠杆倍数并检查结果
            lev = lev_cfg['target_leverage']
            if not self.trader.set_leverage(symbol, lev):
                self.logger.error(f"设置{symbol}杠杆失败，跳过")
                return False
            
            # 检查最大持仓数限制
            max_positions = self.cfg.get('max_positions', 5)
            if max_positions is not None:
                current_positions = self.trader.get_all_positions()
                if len(current_positions) >= max_positions:
                    self.logger.warning(f"当前持仓数已达上限 {max_positions}，跳过 {symbol}")
                    return False
            
            # 计算开仓数量和资金限制
            capital_cfg = self.cfg['capital']
            compounding_cfg = self.cfg.get('compounding', {'enabled': False})
            
            # 获取总资金
            total_balance = self.trader.get_total_balance()
            
            if compounding_cfg.get('enabled', False):
                # 使用复利模式
                # 计算最大开仓金额：≤20 USDT 且 ≤20% 总本金
                max_open_usd = min(
                    20,  # 固定20 USDT上限
                    total_balance * 0.2,  # 20%总本金
                    capital_cfg['day_capital_usd']  # 配置文件中的每日限额
                )
                position_size = max_open_usd / current_price  # 按当前总资金计算仓位
                
                # 记录复利信息
                self.logger.info(f"复利模式 - 总资金: {total_balance:.2f} USDT, "
                               f"最大开仓: {max_open_usd:.2f} USDT, "
                               f"本次开仓: {position_size:.6f} {symbol} (价格: {current_price:.4f})")
            else:
                # 使用固定资金模式
                # 计算最大开仓金额：≤20 USDT 且 ≤20% 总本金
                max_open_usd = min(
                    20,  # 固定20 USDT上限
                    total_balance * 0.2,  # 20%总本金
                    capital_cfg['day_capital_usd']  # 配置文件中的每日限额
                )
                position_size = max_open_usd / current_price  # 使用固定资金
                self.logger.info(f"固定资金模式 - "
                               f"总资金: {total_balance:.2f} USDT, "
                               f"最大开仓: {max_open_usd:.2f} USDT, "
                               f"本次开仓: {position_size:.6f} {symbol} (价格: {current_price:.4f})")
            
            # 检查当日开仓限制 - 在开仓前检查
            if self.today_opened_usd + max_open_usd > capital_cfg['day_capital_usd']:
                self.logger.warning(f"当日开仓已达上限 {capital_cfg['day_capital_usd']:.2f} USDT，跳过")
                return False
                
            # 根据突破方向开仓（只做多，不开空）
            order = None
            if current_price > high and pos_amt == 0:  # 突破新高，开多
                # 再次检查是否超过当日开仓限额（防止并发问题）
                if self.today_opened_usd + max_open_usd > capital_cfg['day_capital_usd']:
                    self.logger.warning(f"当日开仓已达上限 {capital_cfg['day_capital_usd']:.2f} USDT，跳过")
                    return False
                    
                # 开仓
                order = self.trader.place_order(
                    symbol=symbol,
                    side='BUY',
                    order_type='MARKET',
                    quantity=position_size,  # 使用正确计算的数量
                )
                
                if order and 'orderId' in order:
                    open_order_id = order['orderId']
                    self.logger.info(f"[开仓订单] {symbol} 开仓订单已提交，订单ID: {open_order_id}")
                    
                    # 等待并验证订单是否真正成交
                    max_wait = 10  # 最大等待10秒
                    wait_interval = 1  # 每秒检查一次
                    order_filled = False
                    actual_filled_qty = 0
                    actual_entry_price = 0
                    
                    for _ in range(max_wait):
                        is_filled, filled_qty, avg_price = self._check_order_filled(symbol, open_order_id)
                        if is_filled and filled_qty > 0:
                            order_filled = True
                            actual_filled_qty = filled_qty
                            actual_entry_price = avg_price
                            break
                        elif filled_qty > 0:
                            self.logger.info(f"[部分成交] {symbol} 部分成交 {filled_qty}，继续等待...")
                        else:
                            self.logger.debug(f"[等待成交] {symbol} 等待订单成交...")
                        
                        time.sleep(wait_interval)
                    
                    if order_filled:
                        # 订单真正成交，更新当日开仓统计
                        self.today_opened_usd += max_open_usd
                        self.logger.info(f"[开仓成功] {symbol} 突破开仓条件满足")
                        self.logger.info(f"[开仓详情] 新币状态: ≤90天 | 24h涨幅: +{price_change_percent:.1f}% | 5分钟量比: {vol_ratio:.1f}x")
                        self.logger.info(f"[开仓详情] 开仓金额: {max_open_usd:.2f} USDT | 成交价格: {actual_entry_price:.4f} | 成交数量: {actual_filled_qty}")
                        self.logger.info(f"[开仓详情] 开仓订单ID: {open_order_id}")
                        
                        # 计算止损价格
                        stop_loss_price = actual_entry_price * (1 - self.cfg['stop_loss_pct'] / 100)
                        self.logger.info(f"[止损设置] 止损价格计算: {actual_entry_price:.4f} × (1 - {self.cfg['stop_loss_pct']}%) = {stop_loss_price:.4f}")
                        
                        # 立即设置只减仓止损单
                        stop_order_id = self._place_stop_loss_order(symbol, actual_filled_qty, actual_entry_price)
                        if stop_order_id:
                            self.logger.info(f"[止损成功] 止损订单设置成功")
                            self.logger.info(f"[止损详情] 止损订单ID: {stop_order_id} | 止损比例: {self.cfg['stop_loss_pct']}%")
                            return True  # 成功开仓并设置止损
                        else:
                            self.logger.error(f"[止损失败] {symbol} 开仓成功但设置止损失败")
                            self.logger.error(f"[风险提示] 开仓订单ID: {open_order_id} 已成交，但止损订单未成功设置")
                            return False
                    else:
                        # 订单未成交，取消订单
                        self.logger.error(f"[开仓失败] {symbol} 订单提交但未成交，订单ID: {open_order_id}，正在取消订单...")
                        
                        # 取消未成交的订单
                        if self.trader.cancel_order(symbol, open_order_id):
                            self.logger.info(f"[取消成功] {symbol} 未成交订单已取消，订单ID: {open_order_id}")
                        else:
                            self.logger.error(f"[取消失败] {symbol} 取消订单失败，订单ID: {open_order_id}")
                        
                        return False
                        
                elif order:
                    self.logger.error(f"[突破新高] {symbol} 开多仓失败，订单响应异常: {order}")
                else:
                    self.logger.error(f"[突破新高] {symbol} 开多仓失败")
                    
            # 注释掉开空仓的逻辑，因为我们只做多
            # elif current_price < low and pos_amt == 0:  # 跌破新低，开空
            #     # 再次检查是否超过当日开仓限额（防止并发问题）
            #     if self.today_opened_usd + max_open_usd > capital_cfg['day_capital_usd']:
            #         self.logger.warning(f"当日开仓已达上限 {capital_cfg['day_capital_usd']:.2f} USDT，跳过")
            #         return False
            #         
            #     order = self.trader.place_order(
            #         symbol=symbol,
            #         side='SELL', 
            #         order_type='MARKET',
            #         quantity=position_size,  # 使用正确计算的数量
            #     )
            #     if order and 'orderId' in order:
            #         self.today_opened_usd += max_open_usd
            #         self.logger.info(f"[跌破新低] {symbol} 开空仓 {max_open_usd:.2f}U, 订单ID: {order['orderId']}")
            #     elif order:
            #         self.logger.error(f"[跌破新低] {symbol} 开空仓失败，订单响应异常: {order}")
            #     else:
            #         self.logger.error(f"[跌破新低] {symbol} 开空仓失败")
            
            # 如果执行到这里，说明满足了所有条件但没有开仓（可能因为其他原因）
            return True  # 满足条件但未开仓
            
        except Exception as e:
            self.logger.error(f"处理{symbol}时发生错误: {str(e)}")
            return False
            
    def run_forever(self):
        """持续运行策略"""
        self.logger.info("[OK] 策略开始运行...")
        
        while True:
            try:
                # 获取所有永续合约
                futures = self.trader.get_all_futures()
                self.logger.info(f"[OK] 扫描范围：全部{len(futures)}个永续合约")
                
                # 统计信息
                valid_coins_count = 0  # 真正符合条件的币数
                processed_coins_count = 0  # 处理的币数
                
                # 处理每个交易对
                for symbol in futures:
                    result = self.process_symbol(symbol)
                    processed_coins_count += 1
                    
                    # 只有真正符合条件并处理成功的币才计数
                    if result:
                        valid_coins_count += 1
                        
                    # 添加延迟以避免API限制
                    time.sleep(0.1)
                
                self.logger.info(f"本轮处理完成: 处理了 {processed_coins_count} 个币, 符合条件 {valid_coins_count} 个")
                
                # 等待一段时间后继续
                time.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                self.logger.error(f"策略运行时发生错误: {str(e)}")
                time.sleep(60)  # 出错后等待1分钟继续