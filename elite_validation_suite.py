#!/usr/bin/env python3
"""
精英策略验证套件 - 数据驱动的实证测试
包含：信噪比验证、保本移动止盈回测、时段波动分析
"""

import requests
import pandas as pd
import numpy as np
import json
import time
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns

class EliteStrategyValidator:
    def __init__(self):
        self.base_url = "https://api.binance.com/api/v3"
        self.results = {}
        
    def get_klines(self, symbol, interval, limit=1000):
        """获取K线数据"""
        url = f"{self.base_url}/klines"
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }
        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            df = pd.DataFrame(data, columns=[
                'open_time', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'trades', 'taker_buy_base',
                'taker_buy_quote', 'ignore'
            ])
            
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = df[col].astype(float)
            
            df['timestamp'] = pd.to_datetime(df['open_time'], unit='ms')
            df.set_index('timestamp', inplace=True)
            return df
        except Exception as e:
            print(f"获取{symbol} {interval}数据失败: {e}")
            return None

    def validate_signal_noise_ratio(self, symbol='BTCUSDT'):
        """验证5m vs 3m信噪比"""
        print("=== 验证结论①：5m信噪比优于3m ===")
        
        results = {}
        for interval in ['5m', '3m']:
            df = self.get_klines(symbol, interval, 1000)
            if df is None:
                continue
                
            df['returns'] = np.log(df['close'] / df['close'].shift(1))
            signal = df['returns'].mean()
            noise = df['returns'].std()
            snr = signal / noise if noise != 0 else 0
            
            results[interval] = {
                'signal': signal,
                'noise': noise,
                'snr': snr,
                'count': len(df)
            }
            
            print(f"{interval}: 信号={signal:.6f}, 噪声={noise:.6f}, 信噪比={snr:.4f}")
        
        if '5m' in results and '3m' in results:
            better = "5m" if results['5m']['snr'] > results['3m']['snr'] else "3m"
            print(f"✅ 验证结果：{better}信噪比更优")
            self.results['snr_validation'] = results
        
        return results

    def calculate_atr(self, df, period=14):
        """计算ATR"""
        high = df['high']
        low = df['low']
        close = df['close']
        
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=period).mean()
        return atr

    def validate_take_profit_system(self, symbol='BTCUSDT', days=7):
        """验证保本+移动止盈效果"""
        print("\n=== 验证结论②：保本+移动止盈效果 ===")
        
        # 获取1分钟K线
        df_1m = self.get_klines(symbol, '1m', 1440*days)
        if df_1m is None:
            return None
            
        # 计算5分钟K线用于突破判断
        df_5m = self.get_klines(symbol, '5m', 288*days)
        if df_5m is None:
            return None
        
        # 计算指标
        df_1m['atr_14'] = self.calculate_atr(df_1m, 14)
        
        # 突破条件：收盘价突破20根5m高点
        df_5m['upper_band'] = df_5m['high'].rolling(20).max()
        df_5m['breakout_long'] = (df_5m['close'] > df_5m['upper_band']) & \
                                   (df_5m['volume'] > df_5m['volume'].rolling(20).mean() * 1.5)
        
        # 回测逻辑
        trades = []
        
        # 找到所有突破信号
        breakout_times = df_5m[df_5m['breakout_long']].index
        
        for breakout_time in breakout_times:
            try:
                # 找到对应的1分钟数据
                start_idx = df_1m.index.get_loc(breakout_time, method='nearest')
                if start_idx + 100 >= len(df_1m):
                    continue
                
                # 入场价格
                entry_price = df_1m.iloc[start_idx]['close']
                
                # 初始止损
                initial_sl = entry_price - df_1m.iloc[start_idx]['atr_14'] * 1.5
                
                # 两种策略对比
                # 策略A：固定止损
                fixed_result = None
                for i in range(start_idx + 1, min(start_idx + 100, len(df_1m))):
                    current_price = df_1m.iloc[i]['close']
                    if current_price <= initial_sl:
                        fixed_result = (current_price - entry_price) / entry_price * 100
                        break
                
                # 策略B：保本+移动止盈
                trailing_sl = initial_sl
                breakeven_triggered = False
                for i in range(start_idx + 1, min(start_idx + 100, len(df_1m))):
                    current_price = df_1m.iloc[i]['close']
                    current_atr = df_1m.iloc[i]['atr_14']
                    
                    # 保本触发
                    if not breakeven_triggered and current_price >= entry_price * 1.005:
                        trailing_sl = max(trailing_sl, entry_price * 1.002)
                        breakeven_triggered = True
                    
                    # 移动止盈
                    new_trail = current_price - current_atr * 1.5
                    trailing_sl = max(trailing_sl, new_trail)
                    
                    if current_price <= trailing_sl:
                        trailing_result = (current_price - entry_price) / entry_price * 100
                        break
                else:
                    trailing_result = (current_price - entry_price) / entry_price * 100
                
                if fixed_result is not None and trailing_result is not None:
                    trades.append({
                        'entry_time': breakout_time,
                        'entry_price': entry_price,
                        'fixed_result': fixed_result,
                        'trailing_result': trailing_result,
                        'improvement': trailing_result - fixed_result
                    })
                        
            except Exception as e:
                continue
        
        if trades:
            df_trades = pd.DataFrame(trades)
            
            # 计算统计数据
            fixed_win_rate = (df_trades['fixed_result'] > 0).mean() * 100
            trailing_win_rate = (df_trades['trailing_result'] > 0).mean() * 100
            
            fixed_avg_return = df_trades['fixed_result'].mean()
            trailing_avg_return = df_trades['trailing_result'].mean()
            
            fixed_profit_factor = abs(df_trades[df_trades['fixed_result'] > 0]['fixed_result'].sum() / 
                                    df_trades[df_trades['fixed_result'] < 0]['fixed_result'].sum())
            trailing_profit_factor = abs(df_trades[df_trades['trailing_result'] > 0]['trailing_result'].sum() / 
                                       df_trades[df_trades['trailing_result'] < 0]['trailing_result'].sum())
            
            print(f"样本交易数: {len(df_trades)}")
            print(f"固定止损胜率: {fixed_win_rate:.1f}% → 移动止盈胜率: {trailing_win_rate:.1f}%")
            print(f"固定止损平均收益: {fixed_avg_return:.2f}% → 移动止盈: {trailing_avg_return:.2f}%")
            print(f"固定止损盈亏比: {fixed_profit_factor:.2f} → 移动止盈: {trailing_profit_factor:.2f}")
            print(f"平均收益提升: {df_trades['improvement'].mean():.2f}%")
            
            self.results['take_profit_validation'] = {
                'trade_count': len(df_trades),
                'fixed_win_rate': fixed_win_rate,
                'trailing_win_rate': trailing_win_rate,
                'fixed_avg_return': fixed_avg_return,
                'trailing_avg_return': trailing_avg_return,
                'improvement': df_trades['improvement'].mean()
            }
            
            return df_trades
        
        return None

    def validate_time_volatility(self, symbol='BTCUSDT'):
        """验证时段波动差异"""
        print("\n=== 验证结论③：时段波动差异 ===")
        
        df = self.get_klines(symbol, '1h', 168)  # 一周数据
        if df is None:
            return None
            
        df['hour'] = df.index.hour
        df['volatility'] = (df['high'] - df['low']) / df['open'] * 100
        
        # 计算各时段平均波动
        hourly_vol = df.groupby('hour')['volatility'].mean()
        
        # 关键时段对比
        low_vol_period = hourly_vol[0:4].mean()  # 00-04 UTC
        high_vol_period = hourly_vol[12:16].mean()  # 12-16 UTC
        
        print(f"00-04 UTC平均波动: {low_vol_period:.2f}%")
        print(f"12-16 UTC平均波动: {high_vol_period:.2f}%")
        print(f"波动差异: {((high_vol_period - low_vol_period) / high_vol_period * 100):.1f}%")
        
        self.results['time_volatility'] = {
            'low_vol_period': low_vol_period,
            'high_vol_period': high_vol_period,
            'difference_pct': (high_vol_period - low_vol_period) / high_vol_period * 100
        }
        
        return hourly_vol

    def generate_report(self):
        """生成验证报告"""
        print("\n" + "="*50)
        print("精英策略验证报告")
        print("="*50)
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'validations': self.results
        }
        
        # 保存报告
        with open('elite_validation_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print("✅ 验证报告已保存至: elite_validation_report.json")
        return report

    def run_full_validation(self):
        """运行完整验证"""
        print("🚀 启动精英策略实证验证...")
        
        # 验证三个核心结论
        self.validate_signal_noise_ratio()
        self.validate_take_profit_system()
        self.validate_time_volatility()
        
        # 生成报告
        return self.generate_report()

def main():
    """主函数"""
    validator = EliteStrategyValidator()
    
    print("🔬 精英策略数据验证套件")
    print("基于彭博、黄甦弟子、币安广场交易员实证数据")
    print("="*60)
    
    try:
        report = validator.run_full_validation()
        print("\n🎉 验证完成！所有数据已保存")
        
        # 提供快速验证命令
        print("\n📊 快速验证命令:")
        print("python elite_validation_suite.py")
        
    except KeyboardInterrupt:
        print("\n❌ 验证被中断")
    except Exception as e:
        print(f"\n❌ 验证失败: {e}")

if __name__ == "__main__":
    main()