#!/usr/bin/env python3
"""
以UTF-8编码正确显示日志文件内容
"""

import os

def main():
    log_file = 'day_breakout.log'
    
    if not os.path.exists(log_file):
        print(f"日志文件 {log_file} 不存在")
        return
    
    print("以UTF-8编码显示日志文件内容：")
    print("=" * 50)
    
    # 尝试多种编码方式读取文件
    encodings = ['utf-8', 'gbk', 'gb2312', 'ascii']
    
    for encoding in encodings:
        try:
            with open(log_file, 'r', encoding=encoding) as f:
                lines = f.readlines()
                if lines:
                    print(f"使用 {encoding} 编码读取:")
                    print("-" * 30)
                    # 显示最后20行
                    for line in lines[-20:]:
                        print(line.rstrip())
                    print("=" * 50)
                    break
        except UnicodeDecodeError:
            print(f"{encoding} 编码解码失败，尝试下一个编码...")
            continue
        except Exception as e:
            print(f"使用 {encoding} 编码读取文件时出错: {e}")
            continue
    else:
        print("所有编码方式都无法正确读取文件")

if __name__ == '__main__':
    main()