#!/usr/bin/env python3
"""
测试完整的订单流程
"""

import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_full_order_flow():
    try:
        print("=== 测试完整订单流程 ===")
        
        # 导入必要的模块
        from binance_trader import BinanceTrader
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        
        # 获取所有交易对
        all_futures = trader.get_all_futures()
        print(f"总共获取到 {len(all_futures)} 个交易对")
        
        # 选择一个交易对进行测试
        if all_futures:
            symbol = all_futures[0]
            print(f"\n测试交易对: {symbol}")
            
            # 1. 获取当前价格
            ticker = trader.get_symbol_ticker(symbol)
            if ticker and 'price' in ticker:
                current_price = float(ticker['price'])
                print(f"当前价格: {current_price}")
                
                # 2. 获取总资金
                total_balance = trader.get_total_balance()
                print(f"总资金: {total_balance} USDT")
                
                # 3. 计算开仓数量（小额测试）
                if total_balance > 0:
                    # 使用非常小的金额进行测试
                    test_amount = min(5, total_balance * 0.01)  # 使用1%的资金或5 USDT，取较小值
                    position_size = test_amount / current_price
                    print(f"测试开仓金额: {test_amount} USDT")
                    print(f"计算开仓数量: {position_size}")
                    
                    # 4. 设置杠杆
                    print("\n设置杠杆...")
                    if trader.set_leverage(symbol, 10):
                        print("✓ 杠杆设置成功")
                        
                        # 5. 尝试开仓（使用非常小的数量进行测试）
                        print("\n尝试开仓...")
                        # 使用更小的数量确保测试成功
                        test_position_size = position_size * 0.1  # 再减少到10%
                        order = trader.open_position(symbol, 'BUY', test_position_size)
                        
                        if order:
                            print(f"✓ 开仓成功!")
                            print(f"  订单ID: {order.get('orderId', 'N/A')}")
                            print(f"  交易对: {symbol}")
                            print(f"  方向: BUY")
                            print(f"  数量: {order.get('origQty', 'N/A')}")
                        else:
                            print("✗ 开仓失败")
                            
                            # 检查是否有持仓
                            position = trader.get_position(symbol)
                            if position:
                                pos_amt = float(position.get('positionAmt', 0))
                                if pos_amt != 0:
                                    print(f"  但发现已有持仓: {pos_amt}")
                                else:
                                    print("  无持仓")
                            else:
                                print("  无法获取持仓信息")
                    else:
                        print("✗ 杠杆设置失败")
                else:
                    print("资金不足，无法测试开仓")
            else:
                print("无法获取当前价格")
        else:
            print("没有获取到交易对")
            
        print("\n=== 测试完成 ===")
                
    except Exception as e:
        print(f"测试完整订单流程时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_full_order_flow()