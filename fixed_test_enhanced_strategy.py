#!/usr/bin/env python3
"""
修复后的测试增强版策略脚本
"""

import json
import sys
import os
import requests
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def auto_proxy(config):
    """大陆IP→启用代理，阿里云/海外→关闭"""
    try:
        # 测试能否直连 Binance REST
        resp = requests.get('https://fapi.binance.com/fapi/v1/time', timeout=2)
        if resp.status_code == 200:
            config['network']['proxy']['enabled'] = False
            print("直连测试成功，禁用代理")
            return
    except Exception as e:
        print(f"直连测试失败: {e}")
        pass
    # 连不上→默认启用代理
    config['network']['proxy']['enabled'] = True
    print("直连测试失败，启用代理")

def test_enhanced_strategy():
    try:
        # 导入必要的模块
        from day_breakout_strategy import DayBreakoutStrategy
        from binance_trader import BinanceTrader
        from logger_config import setup_logger
        
        # 设置日志
        logger = setup_logger()
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"配置文件加载完成，代理设置: {config['network']['proxy']}")
        auto_proxy(config)   # 自动选代理
        print(f"自动代理设置完成，代理状态: {config['network']['proxy']['enabled']}")
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        
        # 检查是否成功加载交易对
        if not trader.symbols:
            print("警告: 未能加载任何交易对，可能是因为网络连接问题")
            print("将使用默认的BTCUSDT和ETHUSDT进行测试")
            # 手动添加一些测试交易对
            trader.symbols = {
                'BTCUSDT': {
                    'symbol': 'BTCUSDT',
                    'status': 'TRADING',
                    'contractType': 'PERPETUAL',
                    'baseAsset': 'BTC',
                    'quoteAsset': 'USDT',
                    'tick_size': 0.01,
                    'step_size': 0.001,
                    'min_qty': 0.001,
                    'max_qty': 1000000,
                    'min_notional': 0
                },
                'ETHUSDT': {
                    'symbol': 'ETHUSDT',
                    'status': 'TRADING',
                    'contractType': 'PERPETUAL',
                    'baseAsset': 'ETH',
                    'quoteAsset': 'USDT',
                    'tick_size': 0.01,
                    'step_size': 0.001,
                    'min_qty': 0.001,
                    'max_qty': 1000000,
                    'min_notional': 0
                }
            }
        
        # 创建策略实例
        strategy = DayBreakoutStrategy(trader, config)
        
        # 测试新币检查方法
        print("\n测试新币检查方法...")
        test_symbol = "BTCUSDT"
        is_new = strategy._is_new_coin(test_symbol)
        print(f"  {test_symbol} 是否为新币(≤90天): {is_new}")
        
        # 测试杠杆检查方法
        print("\n测试杠杆检查方法...")
        has_leverage = strategy._has_open_leverage(test_symbol)
        print(f"  {test_symbol} 是否已开放杠杆: {has_leverage}")
        
        # 如果无法连接到API，使用模拟数据进行测试
        if not trader.symbols or len(trader.symbols) == 0:
            print("\n由于网络问题，使用模拟数据进行测试...")
            
            # 模拟测试日K筛选条件
            print("\n模拟测试日K筛选条件...")
            price = 50.0
            price_change_percent = 12.5
            quote_volume = 1500000.0
            print(f"  模拟 {test_symbol} 当前价格: {price:.4f} USDT")
            print(f"  模拟 {test_symbol} 24小时涨幅: {price_change_percent:.2f}%")
            print(f"  模拟 {test_symbol} 24小时成交量: {quote_volume:.2f} USDT")
            
            # 检查新的筛选条件
            if price <= 100 and price_change_percent >= 8 and quote_volume >= 1_000_000:
                print(f"  {test_symbol} 符合日K筛选条件(价格≤100, 涨幅≥8%, 成交量≥100万)")
            else:
                print(f"  {test_symbol} 不符合日K筛选条件")
            
            # 模拟测试5m筛选条件
            print("\n模拟测试5m筛选条件...")
            recent_change_pct = 5.2
            vol_ratio = 2.1
            print(f"  模拟 {test_symbol} 最近5m涨幅: {recent_change_pct:.2f}%")
            print(f"  模拟 {test_symbol} 成交量比率: {vol_ratio:.2f}x")
            
            # 检查5m筛选条件
            if recent_change_pct >= 3 and vol_ratio >= 1.5:
                print(f"  {test_symbol} 符合5m筛选条件(涨幅≥3%, 成交量≥1.5x)")
            else:
                print(f"  {test_symbol} 不符合5m筛选条件")
                
            # 模拟测试ATR计算方法
            print("\n模拟测试ATR计算方法...")
            atr_value = 4.5
            print(f"  模拟 {test_symbol} 的ATR 3倍值: {atr_value:.2f}%")
            
            # 模拟测试开仓金额计算
            print("\n模拟测试开仓金额计算...")
            total_balance = 150.0
            print(f"  模拟总资金: {total_balance:.2f} USDT")
            
            # 计算最大开仓金额：≤20 USDT 且 ≤20% 总本金
            max_open_usd = min(20, total_balance * 0.2)
            print(f"  最大开仓金额: {max_open_usd:.2f} USDT")
        else:
            # 正常测试日K筛选条件
            print("\n测试日K筛选条件...")
            ticker = trader.get_symbol_ticker(test_symbol)
            if ticker:
                price = float(ticker['price'])
                print(f"  {test_symbol} 当前价格: {price:.4f} USDT")
                
                # 获取24小时统计信息
                ticker_24h = trader.get_ticker(test_symbol)
                if ticker_24h:
                    price_change_percent = float(ticker_24h.get('priceChangePercent', 0))
                    quote_volume = float(ticker_24h.get('quoteVolume', 0))
                    print(f"  {test_symbol} 24小时涨幅: {price_change_percent:.2f}%")
                    print(f"  {test_symbol} 24小时成交量: {quote_volume:.2f} USDT")
                    
                    # 检查新的筛选条件
                    if price <= 100 and price_change_percent >= 8 and quote_volume >= 1_000_000:
                        print(f"  {test_symbol} 符合日K筛选条件(价格≤100, 涨幅≥8%, 成交量≥100万)")
                    else:
                        print(f"  {test_symbol} 不符合日K筛选条件")
                else:
                    print(f"  获取{test_symbol} 24小时统计信息失败")
            else:
                print(f"  获取{test_symbol}价格失败")
            
            # 测试5m筛选条件
            print("\n测试5m筛选条件...")
            klines = trader.get_klines(test_symbol, interval='5m', limit=10)
            if klines and len(klines) >= 2:
                # 获取最近两根K线的数据
                recent_kline = klines[-1]  # 最近的K线
                prev_kline = klines[-2]    # 前一根K线
                
                # 计算5m涨幅
                recent_open = float(recent_kline[1])
                recent_close = float(recent_kline[4])
                recent_change_pct = ((recent_close - recent_open) / recent_open) * 100
                
                # 计算成交量比率
                recent_vol = float(recent_kline[5])
                prev_vol = float(prev_kline[5])
                vol_ratio = recent_vol / prev_vol if prev_vol > 0 else 0
                
                print(f"  {test_symbol} 最近5m涨幅: {recent_change_pct:.2f}%")
                print(f"  {test_symbol} 成交量比率: {vol_ratio:.2f}x")
                
                # 检查5m筛选条件
                if recent_change_pct >= 3 and vol_ratio >= 1.5:
                    print(f"  {test_symbol} 符合5m筛选条件(涨幅≥3%, 成交量≥1.5x)")
                else:
                    print(f"  {test_symbol} 不符合5m筛选条件")
            else:
                print(f"  获取{test_symbol} 5m K线数据失败")
            
            # 测试ATR计算方法
            print("\n测试ATR计算方法...")
            atr_value = strategy._calculate_atr_3x(test_symbol)
            print(f"  {test_symbol} 的ATR 3倍值: {atr_value:.2f}%")
            
            # 测试开仓金额计算
            print("\n测试开仓金额计算...")
            total_balance = trader.get_total_balance()
            print(f"  总资金: {total_balance:.2f} USDT")
            
            # 计算最大开仓金额：≤20 USDT 且 ≤20% 总本金
            max_open_usd = min(20, total_balance * 0.2)
            print(f"  最大开仓金额: {max_open_usd:.2f} USDT")
        
        print("\n增强版策略测试完成!")
        print("\n主要优化内容:")
        print("1. 日K筛选：24h涨幅≥8%且成交量≥100万USD")
        print("2. 5m筛选：5m涨幅≥3%且成交量≥1.5×均量")
        print("3. 价格上限：≤100 USDT")
        print("4. 逐仓已开放：交易所已开放逐仓杠杆")
        print("5. 更准确的新币检查（≤90天）")
        print("6. 更详细的日志输出")
        
    except Exception as e:
        print(f"测试增强版策略时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_strategy()