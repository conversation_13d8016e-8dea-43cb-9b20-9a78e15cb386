import logging, sys
from logging.handlers import RotatingFileHandler
from datetime import datetime

# 全局变量用于跟踪已配置的日志记录器
_configured_loggers = set()

def setup_logger(name="day_breakout"):
    """配置日志记录器
    
    Args:
        name: 日志记录器名称,默认为day_breakout
        
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    # 检查是否已经配置过这个名称的日志记录器
    if name in _configured_loggers:
        return logging.getLogger(name)
    
    # 获取日志记录器
    log = logging.getLogger(name)
    
    # 清除现有的所有处理器，防止重复
    for handler in log.handlers[:]:
        log.removeHandler(handler)
    
    log.setLevel(logging.INFO)
    
    # 设置简洁的日志格式
    fmt = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s', 
                          datefmt='%Y-%m-%d %H:%M:%S')
    
    # 控制台输出
    sh = logging.StreamHandler(sys.stdout)
    sh.setFormatter(fmt)
    log.addHandler(sh)
    
    # 文件输出(按天轮转)
    fh = RotatingFileHandler(
        f"{name}.log",
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8',
        errors='replace'
    )
    fh.setFormatter(fmt)
    log.addHandler(fh)
    
    # 过滤掉HTTP调试信息
    log.addFilter(lambda record: 'http_client' not in record.getMessage().lower())
    
    # 标记为已配置
    _configured_loggers.add(name)
    
    return log

def devil_banner(config, trader):
    """打印策略启动信息
    
    Args:
        config: 策略配置
        trader: 交易对象
    """
    log = logging.getLogger("day_breakout")
    log.info("Day-Breakout Strategy Starting...")
    
    # 基础信息
    balance = trader.get_total_balance()
    log.info(f"账户余额: {balance:.2f} USDT")
    
    # 交易窗口设置
    trade = config['trade_session']
    start_hour = trade['start_hour']  # 配置中已经是北京时间
    end_hour = trade['end_hour']
    start_minute = trade['start_minute']
    end_minute = trade['end_minute']
    log.info(f"交易时段: {start_hour:02d}:{start_minute:02d} - {end_hour:02d}:{end_minute:02d}")
    
    # 复利设置
    compound = config.get('compounding', {})
    if compound.get('enabled', False):
        withdraw = compound.get('withdraw_pct', 0.8) * 100
        max_open = compound.get('max_open_pct', 0.2) * 100
        log.info(f"复利模式: 已启用 (每日提取{withdraw:.0f}%, 最大开仓{max_open:.0f}%)")
    else:
        log.info("复利模式: 已禁用 (使用固定开仓数量)")
    
    # 风险控制
    risk = config.get('risk', {})
    if risk:
        log.info(f"风险设置:")
        log.info(f"- 最大持仓数: {config.get('max_positions', '无限制')}")
        log.info(f"- 每日最大亏损: {risk.get('max_daily_loss_usd', '无限制')} USDT")
        log.info(f"- 杠杆倍数: {config['leverage']['target_leverage']}x")
    
    # ⑥ 复利检查
    if config.get('auto_compound', {}).get('enabled', False):
        compound_cfg = config['auto_compound']
        daily_reinvest = compound_cfg.get('daily_reinvest', 0.8)
        threshold_pct = compound_cfg.get('threshold_pct', 0.2)
        log.info(f"[OK] 复利已启用：日终{daily_reinvest*100}%转回现货，阈值={threshold_pct*100}%总本金")
    else:
        log.info("[OK] 复利已禁用")