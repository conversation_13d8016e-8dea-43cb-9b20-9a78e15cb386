#!/usr/bin/env python3
"""
测试突破策略逻辑的脚本
"""

import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_breakout_logic():
    try:
        # 导入必要的模块
        from binance_trader import BinanceTrader
        from logger_config import setup_logger
        
        # 设置日志
        logger = setup_logger("test_breakout")
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        
        # 测试几个交易对
        test_symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT"]
        
        for symbol in test_symbols:
            logger.info(f"\n=== 测试交易对: {symbol} ===")
            
            # 获取K线数据
            klines = trader.get_klines(symbol, interval='5m', limit=10)
            if not klines:
                logger.warning(f"无法获取 {symbol} 的K线数据")
                continue
                
            # 显示K线数据
            logger.info(f"获取到 {len(klines)} 根K线")
            for i, k in enumerate(klines):
                logger.info(f"  K线 {i}: 时间={k[0]}, 开盘={k[1]}, 最高={k[2]}, 最低={k[3]}, 收盘={k[4]}")
            
            # 计算动态指标
            highs = [float(k[2]) for k in klines]
            lows = [float(k[3]) for k in klines]
            high = max(highs[:-1])  # 不包含当前K线
            low = min(lows[:-1])
            curr_price = float(klines[-1][4])  # 当前收盘价
            
            logger.info(f"历史最高价(不含最新K线): {high:.4f}")
            logger.info(f"历史最低价(不含最新K线): {low:.4f}")
            logger.info(f"当前价格(最新K线收盘价): {curr_price:.4f}")
            
            # 检查突破条件
            if curr_price > high:
                logger.info(f"★★★ {symbol} 突破新高! 当前价格 {curr_price:.4f} > 历史最高 {high:.4f}")
            elif curr_price < low:
                logger.info(f"★★★ {symbol} 跌破新低! 当前价格 {curr_price:.4f} < 历史最低 {low:.4f}")
            else:
                logger.info(f"--- {symbol} 未突破阈值")
                
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_breakout_logic()