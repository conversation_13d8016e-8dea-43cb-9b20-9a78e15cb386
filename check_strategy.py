import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    import day_breakout_strategy
    print("✅ day_breakout_strategy module imported successfully")
    print(f"Module file: {day_breakout_strategy.__file__}")
    
    # 检查是否有DayBreakoutStrategy类
    if hasattr(day_breakout_strategy, 'DayBreakoutStrategy'):
        print("✅ DayBreakoutStrategy class found")
    else:
        print("❌ DayBreakoutStrategy class not found")
        print(f"Available attributes: {[attr for attr in dir(day_breakout_strategy) if not attr.startswith('_')]}")
        
except Exception as e:
    print(f"❌ Error importing day_breakout_strategy: {e}")
    import traceback
    traceback.print_exc()