#!/usr/bin/env python3
"""
检查当前持仓数量的脚本
"""

import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_positions():
    try:
        # 导入必要的模块
        from binance_trader import BinanceTrader
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        
        # 获取当前持仓
        positions = trader.get_all_positions()
        
        print(f"当前持仓数量: {len(positions)}")
        
        # 显示最大持仓配置
        max_positions = config.get('max_positions', None)
        print(f"最大持仓限制: {max_positions if max_positions is not None else '无限制'}")
        
        if positions:
            print("\n当前持仓详情:")
            for symbol, pos in positions.items():
                qty = float(pos['positionAmt'])
                entry_price = float(pos['entryPrice'])
                unrealized_pnl = float(pos['unRealizedProfit'] or 0)
                print(f"  {symbol}: {qty} (入场价: {entry_price}, 未实现盈亏: {unrealized_pnl:.2f})")
        else:
            print("\n当前无持仓")
            
    except Exception as e:
        print(f"检查持仓时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_positions()