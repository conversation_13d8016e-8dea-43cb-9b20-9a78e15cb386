# advanced_plugin.py
"""
高级功能插件模块
支持浮盈加仓和移动止盈功能
"""


class AdvancedPlugin:
    def __init__(self, config):
        self.cfg = config.get('advanced', {})
        
    def on_profit(self, symbol, profit_pct, position):
        """
        在有浮盈时调用此方法
        :param symbol: 交易对符号
        :param profit_pct: 浮盈百分比
        :param position: 当前持仓信息
        :return: 操作结果
        """
        # 浮盈加仓
        if self.cfg.get('enable_add', False) and profit_pct >= self.cfg.get('add_threshold_pct', 5):
            return self.create_add_order(symbol, position)
        
        # 移动止盈
        if self.cfg.get('enable_trail', False) and profit_pct >= self.cfg.get('trail_stop_pct', 3):
            return self.update_trailing_stop(symbol, position)
            
        return None
    
    def create_add_order(self, symbol, position):
        """
        创建加仓订单
        :param symbol: 交易对符号
        :param position: 当前持仓信息
        :return: 订单信息
        """
        # 这里实现加仓逻辑
        print(f"创建 {symbol} 的加仓订单")
        return {"action": "add_position", "symbol": symbol}
    
    def update_trailing_stop(self, symbol, position):
        """
        更新移动止盈
        :param symbol: 交易对符号
        :param position: 当前持仓信息
        :return: 止盈信息
        """
        # 这里实现移动止盈逻辑
        print(f"更新 {symbol} 的移动止盈")
        return {"action": "trailing_stop", "symbol": symbol}