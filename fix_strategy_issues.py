#!/usr/bin/env python3
"""
修复脚本 - 解决策略运行中的问题
"""

import json
import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')

def fix_strategy_issues():
    try:
        print("=== 修复策略运行中的问题 ===")
        
        # 1. 检查并修复配置文件
        print("1. 检查配置文件...")
        if os.path.exists('config.json'):
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 确保配置中有必要的字段
            if 'trade_session' not in config:
                config['trade_session'] = {
                    "start_hour": 9,
                    "start_minute": 30,
                    "end_hour": 16,
                    "end_minute": 0
                }
                print("  已添加默认交易时段配置")
            
            # 确保有止损和止盈配置
            if 'stop_loss_pct' not in config:
                config['stop_loss_pct'] = 3
            if 'take_profit_pct' not in config:
                config['take_profit_pct'] = 5
                
            # 保存修复后的配置
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            print("  配置文件检查完成")
        else:
            print("  错误: 找不到配置文件 config.json")
            return False
            
        # 2. 检查策略文件
        print("2. 检查策略文件...")
        strategy_files = [
            'day_breakout_strategy.py',
            'detailed_day_breakout_strategy.py'
        ]
        
        for strategy_file in strategy_files:
            if os.path.exists(strategy_file):
                print(f"  找到策略文件: {strategy_file}")
            else:
                print(f"  警告: 找不到策略文件 {strategy_file}")
        
        # 3. 检查交易器
        print("3. 检查交易器...")
        try:
            from binance_trader import BinanceTrader
            print("  BinanceTrader 导入成功")
        except Exception as e:
            print(f"  错误: 无法导入 BinanceTrader: {e}")
            return False
            
        # 4. 检查API密钥
        print("4. 检查API配置...")
        required_keys = ['api_key', 'api_secret']
        missing_keys = [key for key in required_keys if key not in config or not config[key]]
        if missing_keys:
            print(f"  警告: 缺少配置项: {', '.join(missing_keys)}")
        else:
            print("  API配置检查完成")
            
        # 5. 创建启动脚本
        print("5. 创建启动脚本...")
        start_script_content = '''#!/usr/bin/env python3
from detailed_day_breakout_strategy import DayBreakoutStrategy
from binance_trader import BinanceTrader
from logger_config import setup_logger, devil_banner
import json
import pathlib
import socket
import requests

def auto_proxy(config):
    """大陆IP→启用代理，阿里云/海外→关闭"""
    try:
        # 测试能否直连 Binance REST
        resp = requests.get('https://fapi.binance.com/fapi/v1/time', timeout=2)
        if resp.status_code == 200:
            config['network']['proxy']['enabled'] = False
            setup_logger().info("直连测试成功，禁用代理")
            return
    except Exception as e:
        setup_logger().error(f"直连测试失败: {e}")
        pass
    # 连不上→默认启用代理
    config['network']['proxy']['enabled'] = True
    setup_logger().info("直连测试失败，启用代理")

def main():
    logger = setup_logger()
    cfg = json.loads(pathlib.Path('config.json').read_text())
    logger.info(f"配置文件加载完成，代理设置: {cfg['network']['proxy']}")
    auto_proxy(cfg)   # 自动选代理
    logger.info(f"自动代理设置完成，代理状态: {cfg['network']['proxy']['enabled']}")
    trader = BinanceTrader(cfg)
    strategy = DayBreakoutStrategy(trader, cfg)
    
    # 检查是否启用魔鬼级日志
    if cfg.get('logger', {}).get('startup_banner', False):
        devil_banner(cfg, trader)
    else:
        logger.info("Day-Breakout 620-line strategy started")
    
    strategy.run_forever()

if __name__ == '__main__':
    main()
'''
        
        with open('start_fixed_strategy.py', 'w', encoding='utf-8') as f:
            f.write(start_script_content)
        print("  启动脚本 start_fixed_strategy.py 创建完成")
        
        print("\n=== 修复完成 ===")
        print("请使用以下命令运行修复后的策略:")
        print("python start_fixed_strategy.py")
        
        return True
        
    except Exception as e:
        print(f"修复过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    fix_strategy_issues()