#!/usr/bin/env python3
"""
测试优化后的策略
"""

import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_optimized_strategy():
    try:
        print("=== 测试优化后的策略 ===")
        
        # 检查关键文件是否存在
        required_files = [
            'day_breakout_strategy.py',
            'binance_trader.py',
            'config.json'
        ]
        
        for file in required_files:
            if os.path.exists(file):
                print(f"✓ {file} 存在")
            else:
                print(f"✗ {file} 不存在")
                return
        
        # 导入必要的模块
        from day_breakout_strategy import DayBreakoutStrategy
        from binance_trader import BinanceTrader
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("✓ 配置文件加载成功")
        
        # 创建交易器实例
        trader = BinanceTrader(config)
        print("✓ 交易器实例创建成功")
        
        # 创建策略实例
        strategy = DayBreakoutStrategy(trader, config)
        print("✓ 策略实例创建成功")
        
        # 获取所有交易对
        all_futures = trader.get_all_futures()
        print(f"✓ 获取到 {len(all_futures)} 个交易对")
        
        # 测试新币判断逻辑
        if all_futures:
            symbol = all_futures[0]
            is_new = strategy._is_new_coin(symbol)
            has_leverage = strategy._has_open_leverage(symbol)
            print(f"✓ {symbol} 新币判断: {is_new}")
            print(f"✓ {symbol} 杠杆判断: {has_leverage}")
        
        print("\n=== 优化策略测试完成 ===")
        print("主要优化内容:")
        print("1. 新币筛选条件从≤90天放宽到≤120天")
        print("2. 5分钟筛选条件从涨幅≥3%且成交量≥1.5x放宽到涨幅≥2%且成交量≥1.2x")
        print("3. 24小时筛选条件从涨幅≥8%且成交量≥100万放宽到涨幅≥5%且成交量≥50万")
        print("4. 增加了详细的调试日志")
        print("5. 修正了日志信息的误导性问题")
        
    except Exception as e:
        print(f"测试优化策略时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_optimized_strategy()