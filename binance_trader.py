from http_client import HttpClient
import logging

class BinanceTrader:
    def __init__(self, config):
        # 添加日志以调试API密钥
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"API Key length: {len(config['api_key'])}")
        self.logger.info(f"API Secret length: {len(config['api_secret'])}")
        
        proxy_cfg = config.get('network', {}).get('proxy')
        self.http = HttpClient(config['api_key'], config['api_secret'], proxy_cfg=proxy_cfg)
        self.symbols = self._load_symbols()
        self.logger = logging.getLogger(__name__)
        
        # 保存配置
        self.config = config
        self.leverage_config = config.get('leverage', {
            'mode': 'isolated',
            'target_leverage': 10,
            'cap_at_max': True
        })

    def _load_symbols(self):
        """加载并过滤有效的交易对"""
        ex = self.http.get('/fapi/v1/exchangeInfo')
        
        # 检查API调用是否成功
        if not ex or 'symbols' not in ex:
            self.logger.error(f"获取交易对信息失败: {ex}")
            return {}
        
        valid_symbols = {}
        for s in ex['symbols']:
            # 只选择USDT交易对
            if not s['symbol'].endswith('USDT'):
                continue
            # 只选择永续合约
            if s.get('contractType') != 'PERPETUAL':
                continue
            # 只选择可以交易的币对
            if s.get('status') != 'TRADING':
                continue
            
            # 提取价格和数量精度信息
            filters = s.get('filters', [])
            price_filter = next((f for f in filters if f['filterType'] == 'PRICE_FILTER'), {})
            lot_filter = next((f for f in filters if f['filterType'] == 'LOT_SIZE'), {})
            
            valid_symbols[s['symbol']] = {
                'symbol': s['symbol'],
                'status': s['status'],
                'contractType': s['contractType'],
                'baseAsset': s['baseAsset'],
                'quoteAsset': s['quoteAsset'],
                'tick_size': float(price_filter.get('tickSize', 0.01)),
                'step_size': float(lot_filter.get('stepSize', 0.001)),
                'min_qty': float(lot_filter.get('minQty', 0.001)),
                'max_qty': float(lot_filter.get('maxQty', 1000000)),
                'min_notional': float(lot_filter.get('minNotional', 0))
            }
        
        self.logger.info(f"加载了{len(valid_symbols)}个有效的永续合约交易对")
        return valid_symbols

    def get_ticker(self, symbol):
        return self.http.get('/fapi/v1/ticker/24hr', {'symbol': symbol})

    def get_symbol_ticker(self, symbol):
        """获取单个交易对的最新价格信息"""
        try:
            ticker = self.http.get('/fapi/v1/ticker/price', {'symbol': symbol})
            if isinstance(ticker, dict) and 'price' in ticker:
                return ticker
            else:
                self.logger.warning(f"获取{symbol}价格失败: {ticker}")
                return None
        except Exception as e:
            self.logger.error(f"获取{symbol}价格异常: {str(e)}")
            return None

    def get_klines(self, symbol, interval='5m', limit=48):
        """获取K线数据，包含错误处理"""
        try:
            # 检查交易对是否有效
            if symbol not in self.symbols:
                self.logger.warning(f"跳过无效交易对: {symbol}")
                return []
                
            klines = self.http.get('/fapi/v1/klines', {
                'symbol': symbol,
                'interval': interval,
                'limit': limit
            })
            
            if isinstance(klines, list):
                return klines
            else:
                self.logger.warning(f"获取K线失败: {klines}")
                return []
                
        except Exception as e:
            self.logger.error(f"获取K线异常: {e}")
            return []
            
    def get_position(self, symbol):
        """获取单个交易对的持仓"""
        try:
            position = self.http.get('/fapi/v2/positionRisk', {'symbol': symbol})
            if isinstance(position, list) and len(position) > 0:
                return position[0]
            return None
        except Exception as e:
            self.logger.error(f"获取持仓异常: {e}")
            return None

    def get_all_positions(self):
        """获取所有持仓，返回字典格式"""
        try:
            raw = self.http.get('/fapi/v2/positionRisk')
            if isinstance(raw, list):
                return {p['symbol']: p for p in raw if float(p['positionAmt']) != 0}
            else:
                # 如果不是列表，可能是错误信息
                self.logger.warning(f"Unexpected positionRisk response: {raw}")
                return {}
        except Exception as e:
            self.logger.error(f"Error getting positions: {e}")
            return {}

    def get_all_futures(self):
        return list(self.symbols.keys())

    def set_symbol_leverage(self, symbol, target_leverage=None):
        """设置杠杆，包括检查和设置保证金模式"""
        try:
            # ① 确保交易对存在
            if symbol not in self.symbols:
                raise Exception(f"交易对{symbol}不存在")
            
            # 如果没有指定目标杠杆，使用配置中的值
            if target_leverage is None:
                target_leverage = self.leverage_config['target_leverage']
            
            self.logger.info(f"为{symbol}设置{target_leverage}倍杠杆...")
                
            # ② 检查当前保证金模式
            current_margin = self.http.get_margin_type(symbol)
            if current_margin != 'ISOLATED':
                # 设置为逐仓模式
                result = self.http.set_margin_type(symbol)
                if result and result.get('error'):
                    self.logger.error(f"设置保证金模式失败: {result['error']}")
                    return False
            
            # ③ 设置杠杆倍数，确保传入杠杆倍数
            result = self.http.set_leverage(symbol, int(target_leverage))
            if result and result.get('error'):
                    self.logger.error(f"设置杠杆倍数失败: {result['error']}")
                    return False
                
            self.logger.info(f"成功设置{symbol}杠杆为{target_leverage}倍")
            return True
            
        except Exception as e:
            self.logger.error(f"设置杠杆时发生错误: {str(e)}")
            return False

    def set_leverage(self, symbol, target_leverage=None):
        """兼容旧版本的set_leverage方法"""
        return self.set_symbol_leverage(symbol, target_leverage)

    def place_order(self, symbol, side, order_type, quantity, price=None, 
                   stop_price=None, close_position=None, reduce_only=None, timeInForce=None):
        """下单"""
        # 检查并调整数量精度
        if symbol in self.symbols:
            symbol_info = self.symbols[symbol]
            quantity = self._round_quantity(quantity, symbol_info)
            
            # 如果有价格，也调整价格精度
            if price is not None:
                price = self._round_price(price, symbol_info)
            
            # 如果有止损价格，也调整精度
            if stop_price is not None:
                stop_price = self._round_price(stop_price, symbol_info)
        
        params = {
            'symbol': symbol,
            'side': side,
            'type': order_type,
            'quantity': quantity
        }
        
        # 添加可选参数
        if price is not None:
            params['price'] = price
        if stop_price is not None:
            params['stopPrice'] = stop_price
        if close_position is not None:
            params['closePosition'] = close_position
        if reduce_only is not None:
            params['reduceOnly'] = reduce_only
        if timeInForce is not None:
            params['timeInForce'] = timeInForce
            
        try:
            result = self.http.post('/fapi/v1/order', params)
            # 检查是否有错误
            if result.get('error'):
                self.logger.error(f"下单失败: {result['error']}")
                return None
            
            # 检查是否包含订单ID，确认订单真正成功
            if 'orderId' in result:
                self.logger.info(f"下单成功: 订单ID {result['orderId']}")
                return result
            else:
                self.logger.error(f"下单响应异常: {result}")
                return None
            
        except Exception as e:
            self.logger.error(f"下单异常: {str(e)}")
            return None

    def _round_price(self, price, symbol_info):
        """根据交易对信息调整价格精度"""
        try:
            # 使用更精确的精度调整方法
            tick_size = symbol_info.get('tick_size', 0.01)
            # 使用floor division来确保不超过tick_size的整数倍
            import decimal
            decimal.getcontext().prec = 28
            price_decimal = decimal.Decimal(str(price))
            tick_decimal = decimal.Decimal(str(tick_size))
            rounded_price = float((price_decimal // tick_decimal) * tick_decimal)
            
            self.logger.debug(f"价格精度调整: {price} -> {rounded_price} (tick_size: {tick_size})")
            return rounded_price
            
        except Exception as e:
            self.logger.error(f"调整价格精度时出错: {e}")
            return price

    def _round_quantity(self, quantity, symbol_info):
        """根据交易对信息调整数量精度"""
        try:
            # 使用更精确的精度调整方法
            step_size = symbol_info.get('step_size', 0.001)
            # 使用floor division来确保不超过step_size的整数倍
            import decimal
            decimal.getcontext().prec = 28
            qty_decimal = decimal.Decimal(str(quantity))
            step_decimal = decimal.Decimal(str(step_size))
            rounded_qty = float((qty_decimal // step_decimal) * step_decimal)
            
            # 确保不低于最小数量
            min_qty = symbol_info.get('min_qty', 0.001)
            if rounded_qty < min_qty:
                self.logger.warning(f"调整后数量 {rounded_qty} 低于最小数量 {min_qty}，将使用最小数量")
                rounded_qty = min_qty
            
            self.logger.debug(f"数量精度调整: {quantity} -> {rounded_qty} (step_size: {step_size})")
            return rounded_qty
            
        except Exception as e:
            self.logger.error(f"调整数量精度时出错: {e}")
            return quantity

    def get_total_balance(self):
        """获取总余额"""
        try:
            account = self.http.get('/fapi/v2/balance')
            if isinstance(account, list):
                for asset in account:
                    if asset['asset'] == 'USDT':
                        return float(asset['balance'])
            return 0
        except Exception as e:
            self.logger.error(f"获取余额异常: {str(e)}")
            return 0
            
    def get_order(self, symbol, order_id):
        """获取订单状态"""
        try:
            order = self.http.get('/fapi/v1/order', {
                'symbol': symbol,
                'orderId': order_id
            })
            return order
        except Exception as e:
            self.logger.error(f"获取订单状态异常: {str(e)}")
            return None
            
    def cancel_order(self, symbol, order_id):
        """取消订单"""
        try:
            result = self.http.delete('/fapi/v1/order', {
                'symbol': symbol,
                'orderId': order_id
            })
            if result and 'orderId' in result:
                self.logger.info(f"取消订单成功: {symbol} 订单ID {order_id}")
                return True
            else:
                self.logger.error(f"取消订单失败: {result}")
                return False
        except Exception as e:
            self.logger.error(f"取消订单异常: {str(e)}")
            return False
            
    def open_position(self, symbol, side, size, price=None, timeInForce=None):
        """开仓
        Args:
            symbol: 交易对
            side: 方向,'BUY'或'SELL'
            size: 数量
            price: 价格,不指定则为市价单
            timeInForce: GTC、IOC、FOK等,限价单必须
        """
        try:
            # 设置杠杆
            if not self.set_symbol_leverage(symbol):
                return None
                
            # 确定订单类型
            order_type = 'LIMIT' if price else 'MARKET'
            
            # 如果是限价单并且没有指定timeInForce,默认使用GTC
            if order_type == 'LIMIT' and timeInForce is None:
                timeInForce = 'GTC'
                
            # 下单    
            order = self.place_order(
                symbol=symbol,
                side=side, 
                order_type=order_type,
                quantity=size,
                price=price,
                timeInForce=timeInForce,
                reduce_only=False
            )
            
            if order and 'orderId' in order:
                self.logger.info(f"开仓成功: {symbol} {side} {size}, 订单ID: {order['orderId']}")
                return order
            elif order:
                self.logger.error(f"开仓失败，订单响应异常: {order}")
                return None
            else:
                self.logger.error(f"开仓失败: {symbol} {side} {size}")
                return None
                
        except Exception as e:
            self.logger.error(f"开仓异常: {str(e)}")
            return None
